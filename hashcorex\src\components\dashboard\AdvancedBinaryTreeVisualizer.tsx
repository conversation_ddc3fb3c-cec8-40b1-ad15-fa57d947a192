'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent, Button } from '@/components/ui';
import { Grid } from '@/components/layout';
import { 
  Users, Copy, Share2, TrendingUp, Award, Search, ZoomIn, ZoomOut, 
  Maximize2, ChevronDown, ChevronRight, User, Calendar, Crown,
  Activity, Target, BarChart3
} from 'lucide-react';
import { formatCurrency, formatDate, copyToClipboard, getTimeUntilBinaryPayout } from '@/lib/utils';

interface EnhancedBinaryTreeNode {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    createdAt: string;
    isActive: boolean;
  };
  sponsorInfo?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
  directReferralCount: number;
  teamCounts: {
    left: number;
    right: number;
    total: number;
  };
  binaryPoints: {
    leftPoints: number;
    rightPoints: number;
    matchedPoints: number;
  };
  leftChild?: EnhancedBinaryTreeNode;
  rightChild?: EnhancedBinaryTreeNode;
}

interface EnhancedBinaryTreeData {
  treeStructure: EnhancedBinaryTreeNode;
  statistics: {
    totalDirectReferrals: number;
    leftReferrals: number;
    rightReferrals: number;
    totalCommissions: number;
    detailedStats: {
      directReferrals: number;
      leftTeam: number;
      rightTeam: number;
      totalTeam: number;
      activeMembers: number;
      recentJoins: number;
    };
    treeHealth: {
      totalUsers: number;
      balanceRatio: number;
      averageDepth: number;
      maxDepth: number;
      emptyPositions: number;
    };
    binaryPoints: {
      leftPoints: number;
      rightPoints: number;
      matchedPoints: number;
    };
  };
  referralLinks: {
    left: string;
    right: string;
    general: string;
  };
}

interface TreeViewSettings {
  zoom: number;
  maxDepth: number;
  showInactive: boolean;
  expandedNodes: Set<string>;
  focusedNode: string | null;
}

export const AdvancedBinaryTreeVisualizer: React.FC = () => {
  const [treeData, setTreeData] = useState<EnhancedBinaryTreeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [timeUntilMatching, setTimeUntilMatching] = useState(getTimeUntilBinaryPayout());
  const [viewSettings, setViewSettings] = useState<TreeViewSettings>({
    zoom: 1,
    maxDepth: 5,
    showInactive: true,
    expandedNodes: new Set(),
    focusedNode: null,
  });

  const treeContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchTreeData();
    
    // Update countdown every second
    const interval = setInterval(() => {
      setTimeUntilMatching(getTimeUntilBinaryPayout());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const fetchTreeData = async () => {
    try {
      const response = await fetch(`/api/referrals/tree?depth=${viewSettings.maxDepth}&enhanced=true`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setTreeData(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch enhanced binary tree data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      const response = await fetch(`/api/referrals/search?term=${encodeURIComponent(searchTerm)}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setSearchResults(data.data);
        }
      }
    } catch (error) {
      console.error('Search failed:', error);
    }
  };

  const handleZoom = (direction: 'in' | 'out') => {
    setViewSettings(prev => ({
      ...prev,
      zoom: direction === 'in' 
        ? Math.min(prev.zoom * 1.2, 3) 
        : Math.max(prev.zoom / 1.2, 0.5)
    }));
  };

  const toggleNodeExpansion = (nodeId: string) => {
    setViewSettings(prev => {
      const newExpanded = new Set(prev.expandedNodes);
      if (newExpanded.has(nodeId)) {
        newExpanded.delete(nodeId);
      } else {
        newExpanded.add(nodeId);
      }
      return { ...prev, expandedNodes: newExpanded };
    });
  };

  const focusOnNode = (nodeId: string) => {
    setViewSettings(prev => ({ ...prev, focusedNode: nodeId }));
  };

  const handleCopyLink = async (link: string) => {
    try {
      await copyToClipboard(link);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-96 bg-gray-200 rounded-xl"></div>
        </div>
      </div>
    );
  }

  if (!treeData) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-gray-500">Failed to load enhanced binary tree data</p>
          <Button
            onClick={fetchTreeData}
            className="mt-4"
            variant="outline"
          >
            Retry Loading
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-8">
      {/* Enhanced Statistics Dashboard */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">🚀 Advanced Network Analytics</h2>
        <Grid cols={{ default: 1, sm: 2, lg: 4 }} gap={6}>
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Direct Referrals</p>
                  <p className="text-3xl font-bold text-dark-900">
                    {treeData.statistics?.detailedStats?.directReferrals || 0}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    +{treeData.statistics?.detailedStats?.recentJoins || 0} this month
                  </p>
                </div>
                <div className="h-14 w-14 bg-blue-100 rounded-xl flex items-center justify-center">
                  <Users className="h-7 w-7 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Total Team</p>
                  <p className="text-3xl font-bold text-eco-600">
                    {treeData.statistics?.detailedStats?.totalTeam || 0}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {treeData.statistics?.detailedStats?.activeMembers || 0} active
                  </p>
                </div>
                <div className="h-14 w-14 bg-eco-100 rounded-xl flex items-center justify-center">
                  <Target className="h-7 w-7 text-eco-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Tree Balance</p>
                  <p className="text-3xl font-bold text-solar-600">
                    {Math.round((treeData.statistics?.treeHealth?.balanceRatio || 0) * 100)}%
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    L: {treeData.statistics?.detailedStats?.leftTeam || 0} | R: {treeData.statistics?.detailedStats?.rightTeam || 0}
                  </p>
                </div>
                <div className="h-14 w-14 bg-solar-100 rounded-xl flex items-center justify-center">
                  <BarChart3 className="h-7 w-7 text-solar-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Total Commissions</p>
                  <p className="text-3xl font-bold text-green-600">
                    {formatCurrency(treeData.statistics?.totalCommissions || 0)}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">All time earnings</p>
                </div>
                <div className="h-14 w-14 bg-green-100 rounded-xl flex items-center justify-center">
                  <TrendingUp className="h-7 w-7 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
      </div>

      {/* Tree Controls and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-blue-500" />
              <span>Interactive Binary Tree</span>
            </span>
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleZoom('out')}
                disabled={viewSettings.zoom <= 0.5}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <span className="text-sm text-gray-600 min-w-[60px] text-center">
                {Math.round(viewSettings.zoom * 100)}%
              </span>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleZoom('in')}
                disabled={viewSettings.zoom >= 3}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setViewSettings(prev => ({ ...prev, zoom: 1 }))}
              >
                <Maximize2 className="h-4 w-4" />
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Search Bar */}
          <div className="mb-6">
            <div className="flex space-x-2">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <Button onClick={handleSearch}>
                Search
              </Button>
            </div>

            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Search Results:</h4>
                <div className="space-y-2">
                  {searchResults.map((result) => (
                    <div
                      key={result.id}
                      className="flex items-center justify-between p-2 bg-white rounded border cursor-pointer hover:bg-blue-50"
                      onClick={() => focusOnNode(result.id)}
                    >
                      <div>
                        <p className="text-sm font-medium">
                          {result.firstName} {result.lastName}
                        </p>
                        <p className="text-xs text-gray-500">
                          {result.email} • Path: {result.placementPath} • Gen: {result.generation}
                        </p>
                      </div>
                      <Button size="sm" variant="outline">
                        Focus
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Tree Display Settings */}
          <div className="mb-6 flex flex-wrap items-center gap-4">
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Max Depth:</label>
              <select
                value={viewSettings.maxDepth}
                onChange={(e) => setViewSettings(prev => ({ ...prev, maxDepth: parseInt(e.target.value) }))}
                className="px-3 py-1 border border-gray-300 rounded text-sm"
              >
                <option value={3}>3 Levels</option>
                <option value={5}>5 Levels</option>
                <option value={7}>7 Levels</option>
                <option value={10}>10 Levels</option>
              </select>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="showInactive"
                checked={viewSettings.showInactive}
                onChange={(e) => setViewSettings(prev => ({ ...prev, showInactive: e.target.checked }))}
                className="rounded"
              />
              <label htmlFor="showInactive" className="text-sm font-medium text-gray-700">
                Show Inactive Users
              </label>
            </div>

            <Button
              size="sm"
              variant="outline"
              onClick={fetchTreeData}
            >
              Refresh Tree
            </Button>
          </div>

          {/* Interactive Tree Visualization */}
          <div
            ref={treeContainerRef}
            className="relative overflow-auto border border-gray-200 rounded-lg bg-gray-50"
            style={{ height: '600px' }}
          >
            <div
              className="p-8 min-w-max"
              style={{
                transform: `scale(${viewSettings.zoom})`,
                transformOrigin: 'top left'
              }}
            >
              <EnhancedTreeNodeComponent
                node={treeData.treeStructure}
                position="ROOT"
                depth={0}
                maxDepth={viewSettings.maxDepth}
                viewSettings={viewSettings}
                onToggleExpansion={toggleNodeExpansion}
                onFocusNode={focusOnNode}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Referral Links and Binary Matching */}
      <Grid cols={{ default: 1, lg: 2 }} gap={6}>
        {/* Next Binary Matching */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Award className="h-5 w-5 text-solar-500" />
              <span>Next Binary Matching</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <p className="text-sm text-gray-600 mb-4">
                Daily binary matching at 12:00 AM UTC
              </p>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-solar-600">
                    {timeUntilMatching.hours}
                  </div>
                  <div className="text-xs text-gray-500">Hours</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-solar-600">
                    {timeUntilMatching.minutes}
                  </div>
                  <div className="text-xs text-gray-500">Minutes</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-solar-600">
                    {timeUntilMatching.seconds}
                  </div>
                  <div className="text-xs text-gray-500">Seconds</div>
                </div>
              </div>

              <div className="mt-4 p-3 bg-solar-50 rounded-lg">
                <p className="text-sm text-solar-700">
                  <strong>Potential Match:</strong> {Math.min(treeData.statistics?.binaryPoints?.leftPoints || 0, treeData.statistics?.binaryPoints?.rightPoints || 0)} points
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Referral Links */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Share2 className="h-5 w-5 text-eco-500" />
              <span>Referral Links</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">General Referral Link</label>
                <div className="flex mt-1">
                  <input
                    type="text"
                    value={treeData.referralLinks.general}
                    readOnly
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                  />
                  <Button
                    size="sm"
                    onClick={() => handleCopyLink(treeData.referralLinks.general)}
                    className="rounded-l-none"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Left Side Link</label>
                <div className="flex mt-1">
                  <input
                    type="text"
                    value={treeData.referralLinks.left}
                    readOnly
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                  />
                  <Button
                    size="sm"
                    onClick={() => handleCopyLink(treeData.referralLinks.left)}
                    className="rounded-l-none"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Right Side Link</label>
                <div className="flex mt-1">
                  <input
                    type="text"
                    value={treeData.referralLinks.right}
                    readOnly
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                  />
                  <Button
                    size="sm"
                    onClick={() => handleCopyLink(treeData.referralLinks.right)}
                    className="rounded-l-none"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-xs text-blue-700">
                <strong>Enhanced Placement:</strong> New users are automatically placed in your weaker leg
                for optimal network balance. Use specific side links to target placement.
              </p>
            </div>
          </CardContent>
        </Card>
      </Grid>
    </div>
  );
};

// Enhanced Tree Node Component with detailed information
interface EnhancedTreeNodeProps {
  node: EnhancedBinaryTreeNode | null;
  position: string;
  depth: number;
  maxDepth: number;
  viewSettings: TreeViewSettings;
  onToggleExpansion: (nodeId: string) => void;
  onFocusNode: (nodeId: string) => void;
}

const EnhancedTreeNodeComponent: React.FC<EnhancedTreeNodeProps> = ({
  node,
  position,
  depth,
  maxDepth,
  viewSettings,
  onToggleExpansion,
  onFocusNode,
}) => {
  if (!node) {
    return (
      <div className="flex flex-col items-center">
        <div className="w-32 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
          <span className="text-gray-400 text-xs">Empty</span>
        </div>
        <p className="text-xs text-gray-500 mt-2">{position}</p>
      </div>
    );
  }

  if (!viewSettings.showInactive && !node.user.isActive) {
    return null;
  }

  const isExpanded = viewSettings.expandedNodes.has(node.user.id);
  const isFocused = viewSettings.focusedNode === node.user.id;
  const hasChildren = node.leftChild || node.rightChild;
  const shouldShowChildren = depth < maxDepth && (isExpanded || depth < 2);

  return (
    <div className="flex flex-col items-center space-y-4">
      {/* Enhanced Node Display */}
      <div
        className={`relative w-40 bg-white border-2 rounded-lg p-3 shadow-sm transition-all duration-200 cursor-pointer ${
          isFocused
            ? 'border-blue-500 shadow-lg ring-2 ring-blue-200'
            : node.user.isActive
              ? 'border-eco-300 hover:border-eco-400'
              : 'border-gray-300 opacity-75'
        }`}
        onClick={() => onFocusNode(node.user.id)}
      >
        {/* Expansion Toggle */}
        {hasChildren && depth < maxDepth && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleExpansion(node.user.id);
            }}
            className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-blue-600"
          >
            {isExpanded ? <ChevronDown className="h-3 w-3" /> : <ChevronRight className="h-3 w-3" />}
          </button>
        )}

        {/* User Avatar */}
        <div className="flex items-center space-x-2 mb-2">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-bold ${
            node.user.isActive ? 'bg-eco-500' : 'bg-gray-400'
          }`}>
            {node.user.firstName.charAt(0)}{node.user.lastName.charAt(0)}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-semibold text-gray-900 truncate">
              {node.user.firstName} {node.user.lastName}
            </p>
            <p className="text-xs text-gray-500 truncate">
              {node.user.email}
            </p>
          </div>
        </div>

        {/* User Stats */}
        <div className="space-y-1">
          <div className="flex justify-between text-xs">
            <span className="text-gray-600">Team:</span>
            <span className="font-medium">{node.teamCounts.total}</span>
          </div>
          <div className="flex justify-between text-xs">
            <span className="text-gray-600">Direct:</span>
            <span className="font-medium">{node.directReferralCount}</span>
          </div>
          <div className="flex justify-between text-xs">
            <span className="text-gray-600">L/R:</span>
            <span className="font-medium">{node.teamCounts.left}/{node.teamCounts.right}</span>
          </div>
          <div className="flex justify-between text-xs">
            <span className="text-gray-600">Points:</span>
            <span className="font-medium">{node.binaryPoints.leftPoints}/{node.binaryPoints.rightPoints}</span>
          </div>
        </div>

        {/* Sponsor Info */}
        {node.sponsorInfo && (
          <div className="mt-2 pt-2 border-t border-gray-200">
            <div className="flex items-center space-x-1">
              <Crown className="h-3 w-3 text-yellow-500" />
              <span className="text-xs text-gray-600">
                Sponsor: {node.sponsorInfo.firstName} {node.sponsorInfo.lastName}
              </span>
            </div>
          </div>
        )}

        {/* Status Indicators */}
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center space-x-1">
            <Activity className={`h-3 w-3 ${node.user.isActive ? 'text-green-500' : 'text-gray-400'}`} />
            <span className="text-xs text-gray-600">
              {node.user.isActive ? 'Active' : 'Inactive'}
            </span>
          </div>
          <span className="text-xs text-gray-500">
            {formatDate(node.user.createdAt)}
          </span>
        </div>
      </div>

      <p className="text-xs text-gray-500 font-medium">{position}</p>

      {/* Children */}
      {shouldShowChildren && hasChildren && (
        <div className="flex justify-center space-x-16">
          <EnhancedTreeNodeComponent
            node={node.leftChild || null}
            position="Left"
            depth={depth + 1}
            maxDepth={maxDepth}
            viewSettings={viewSettings}
            onToggleExpansion={onToggleExpansion}
            onFocusNode={onFocusNode}
          />
          <EnhancedTreeNodeComponent
            node={node.rightChild || null}
            position="Right"
            depth={depth + 1}
            maxDepth={maxDepth}
            viewSettings={viewSettings}
            onToggleExpansion={onToggleExpansion}
            onFocusNode={onFocusNode}
          />
        </div>
      )}
    </div>
  );
};
