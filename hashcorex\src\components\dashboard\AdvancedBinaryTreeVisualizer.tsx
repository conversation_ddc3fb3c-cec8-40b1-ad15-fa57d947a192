'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent, Button } from '@/components/ui';
import { Grid } from '@/components/layout';
import { 
  Users, Copy, Share2, TrendingUp, Award, Search, ZoomIn, ZoomOut, 
  Maximize2, ChevronDown, ChevronRight, User, Calendar, Crown,
  Activity, Target, BarChart3
} from 'lucide-react';
import { formatCurrency, formatDate, copyToClipboard, getTimeUntilBinaryPayout } from '@/lib/utils';

interface EnhancedBinaryTreeNode {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    createdAt: string;
    isActive: boolean;
  };
  sponsorInfo?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
  directReferralCount: number;
  teamCounts: {
    left: number;
    right: number;
    total: number;
  };
  binaryPoints: {
    leftPoints: number;
    rightPoints: number;
    matchedPoints: number;
  };
  leftChild?: EnhancedBinaryTreeNode;
  rightChild?: EnhancedBinaryTreeNode;
}

interface EnhancedBinaryTreeData {
  treeStructure: EnhancedBinaryTreeNode;
  statistics: {
    totalDirectReferrals: number;
    leftReferrals: number;
    rightReferrals: number;
    totalCommissions: number;
    detailedStats: {
      directReferrals: number;
      leftTeam: number;
      rightTeam: number;
      totalTeam: number;
      activeMembers: number;
      recentJoins: number;
    };
    treeHealth: {
      totalUsers: number;
      balanceRatio: number;
      averageDepth: number;
      maxDepth: number;
      emptyPositions: number;
    };
    binaryPoints: {
      leftPoints: number;
      rightPoints: number;
      matchedPoints: number;
    };
  };
  referralLinks: {
    left: string;
    right: string;
    general: string;
  };
}

interface TreeViewSettings {
  zoom: number;
  maxDepth: number;
  showInactive: boolean;
  expandedNodes: Set<string>;
  focusedNode: string | null;
}

// Add custom CSS for animations
const treeAnimationStyles = `
  @keyframes fadeInScale {
    0% {
      opacity: 0;
      transform: scale(0.8);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  .hover\\:scale-102:hover {
    transform: scale(1.02);
  }
`;

export const AdvancedBinaryTreeVisualizer: React.FC = () => {
  const [treeData, setTreeData] = useState<EnhancedBinaryTreeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [timeUntilMatching, setTimeUntilMatching] = useState(getTimeUntilBinaryPayout());
  const [isMobile, setIsMobile] = useState(false);
  const [viewSettings, setViewSettings] = useState<TreeViewSettings>({
    zoom: 1,
    maxDepth: 5,
    showInactive: true,
    expandedNodes: new Set(),
    focusedNode: null,
  });

  const treeContainerRef = useRef<HTMLDivElement>(null);

  // Handle responsive design
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);

      // Auto-adjust depth for mobile
      if (mobile && viewSettings.maxDepth > 4) {
        setViewSettings(prev => ({ ...prev, maxDepth: 4 }));
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [viewSettings.maxDepth]);

  useEffect(() => {
    fetchTreeData();
    
    // Update countdown every second
    const interval = setInterval(() => {
      setTimeUntilMatching(getTimeUntilBinaryPayout());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const fetchTreeData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/referrals/tree?depth=${viewSettings.maxDepth}&enhanced=true`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setTreeData(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch enhanced binary tree data:', error);
    } finally {
      setLoading(false);
    }
  }, [viewSettings.maxDepth]);

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      const response = await fetch(`/api/referrals/search?term=${encodeURIComponent(searchTerm)}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setSearchResults(data.data);
        }
      }
    } catch (error) {
      console.error('Search failed:', error);
    }
  };

  const handleZoom = (direction: 'in' | 'out') => {
    setViewSettings(prev => ({
      ...prev,
      zoom: direction === 'in' 
        ? Math.min(prev.zoom * 1.2, 3) 
        : Math.max(prev.zoom / 1.2, 0.5)
    }));
  };

  const toggleNodeExpansion = (nodeId: string) => {
    setViewSettings(prev => {
      const newExpanded = new Set(prev.expandedNodes);
      if (newExpanded.has(nodeId)) {
        newExpanded.delete(nodeId);
      } else {
        newExpanded.add(nodeId);
      }
      return { ...prev, expandedNodes: newExpanded };
    });
  };

  const focusOnNode = (nodeId: string) => {
    setViewSettings(prev => ({ ...prev, focusedNode: nodeId }));
  };

  const handleCopyLink = async (link: string) => {
    try {
      await copyToClipboard(link);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  // Calculate responsive dimensions
  const getResponsiveDimensions = useCallback(() => {
    const baseWidth = isMobile ? 600 : 800;
    const nodeSpacing = isMobile ? 160 : 200;
    const containerWidth = Math.max(
      baseWidth,
      Math.pow(2, Math.min(viewSettings.maxDepth, 6)) * nodeSpacing
    );

    return {
      containerWidth,
      nodeWidth: isMobile ? 140 : 180,
      nodeHeight: isMobile ? 120 : 140,
      verticalSpacing: isMobile ? 140 : 180,
    };
  }, [isMobile, viewSettings.maxDepth]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-96 bg-gray-200 rounded-xl"></div>
        </div>
      </div>
    );
  }

  if (!treeData) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-gray-500">Failed to load enhanced binary tree data</p>
          <Button
            onClick={fetchTreeData}
            className="mt-4"
            variant="outline"
          >
            Retry Loading
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-8">
      {/* Inject custom CSS */}
      <style dangerouslySetInnerHTML={{ __html: treeAnimationStyles }} />

      {/* Enhanced Statistics Dashboard */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">🚀 Advanced Network Analytics</h2>
        <Grid cols={{ default: 1, sm: 2, lg: 4 }} gap={6}>
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Direct Referrals</p>
                  <p className="text-3xl font-bold text-dark-900">
                    {treeData.statistics?.detailedStats?.directReferrals || 0}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    +{treeData.statistics?.detailedStats?.recentJoins || 0} this month
                  </p>
                </div>
                <div className="h-14 w-14 bg-blue-100 rounded-xl flex items-center justify-center">
                  <Users className="h-7 w-7 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Total Team</p>
                  <p className="text-3xl font-bold text-eco-600">
                    {treeData.statistics?.detailedStats?.totalTeam || 0}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {treeData.statistics?.detailedStats?.activeMembers || 0} active
                  </p>
                </div>
                <div className="h-14 w-14 bg-eco-100 rounded-xl flex items-center justify-center">
                  <Target className="h-7 w-7 text-eco-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Tree Balance</p>
                  <p className="text-3xl font-bold text-solar-600">
                    {Math.round((treeData.statistics?.treeHealth?.balanceRatio || 0) * 100)}%
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    L: {treeData.statistics?.detailedStats?.leftTeam || 0} | R: {treeData.statistics?.detailedStats?.rightTeam || 0}
                  </p>
                </div>
                <div className="h-14 w-14 bg-solar-100 rounded-xl flex items-center justify-center">
                  <BarChart3 className="h-7 w-7 text-solar-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Total Commissions</p>
                  <p className="text-3xl font-bold text-green-600">
                    {formatCurrency(treeData.statistics?.totalCommissions || 0)}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">All time earnings</p>
                </div>
                <div className="h-14 w-14 bg-green-100 rounded-xl flex items-center justify-center">
                  <TrendingUp className="h-7 w-7 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
      </div>

      {/* Tree Controls and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-blue-500" />
              <span>Interactive Binary Tree</span>
            </span>
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleZoom('out')}
                disabled={viewSettings.zoom <= 0.5}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <span className="text-sm text-gray-600 min-w-[60px] text-center">
                {Math.round(viewSettings.zoom * 100)}%
              </span>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleZoom('in')}
                disabled={viewSettings.zoom >= 3}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setViewSettings(prev => ({ ...prev, zoom: 1 }))}
              >
                <Maximize2 className="h-4 w-4" />
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Search Bar */}
          <div className="mb-6">
            <div className="flex space-x-2">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <Button onClick={handleSearch}>
                Search
              </Button>
            </div>

            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Search Results:</h4>
                <div className="space-y-2">
                  {searchResults.map((result) => (
                    <div
                      key={result.id}
                      className="flex items-center justify-between p-2 bg-white rounded border cursor-pointer hover:bg-blue-50"
                      onClick={() => focusOnNode(result.id)}
                    >
                      <div>
                        <p className="text-sm font-medium">
                          {result.firstName} {result.lastName}
                        </p>
                        <p className="text-xs text-gray-500">
                          {result.email} • Path: {result.placementPath} • Gen: {result.generation}
                        </p>
                      </div>
                      <Button size="sm" variant="outline">
                        Focus
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Tree Display Settings */}
          <div className="mb-6 flex flex-wrap items-center gap-4">
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Max Depth:</label>
              <select
                value={viewSettings.maxDepth}
                onChange={(e) => {
                  const newDepth = parseInt(e.target.value);
                  setViewSettings(prev => ({
                    ...prev,
                    maxDepth: newDepth,
                    // Auto-collapse nodes if reducing depth
                    expandedNodes: newDepth < prev.maxDepth ? new Set() : prev.expandedNodes
                  }));
                  // Refetch data with new depth
                  fetchTreeData();
                }}
                className="px-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500"
              >
                <option value={3}>3 Levels (Mobile)</option>
                <option value={4}>4 Levels</option>
                <option value={5}>5 Levels (Recommended)</option>
                <option value={6}>6 Levels</option>
                <option value={7}>7 Levels (Desktop)</option>
                <option value={8}>8 Levels (Large Screen)</option>
              </select>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="showInactive"
                checked={viewSettings.showInactive}
                onChange={(e) => setViewSettings(prev => ({ ...prev, showInactive: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="showInactive" className="text-sm font-medium text-gray-700">
                Show Inactive Users
              </label>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setViewSettings(prev => ({
                  ...prev,
                  expandedNodes: new Set(),
                  focusedNode: null
                }))}
              >
                Collapse All
              </Button>

              <Button
                size="sm"
                variant="outline"
                onClick={fetchTreeData}
              >
                Refresh Tree
              </Button>
            </div>

            {/* Tree Stats */}
            <div className="ml-auto flex items-center space-x-4 text-sm text-gray-600">
              <span>
                Showing: {Math.min(Math.pow(2, viewSettings.maxDepth) - 1, treeData.statistics?.detailedStats?.totalTeam || 0)} nodes
              </span>
              <span>
                Balance: {Math.round((treeData.statistics?.treeHealth?.balanceRatio || 0) * 100)}%
              </span>
            </div>
          </div>

          {/* Interactive Tree Visualization */}
          <div
            ref={treeContainerRef}
            className="relative overflow-auto border border-gray-200 rounded-lg bg-gradient-to-br from-slate-50 to-blue-50"
            style={{ height: isMobile ? '500px' : '700px' }}
          >
            {loading && (
              <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading network tree...</p>
                </div>
              </div>
            )}
            <div
              className="relative min-w-max min-h-full flex items-start justify-center pt-8"
              style={{
                transform: `scale(${viewSettings.zoom})`,
                transformOrigin: 'top center',
                minWidth: `${getResponsiveDimensions().containerWidth}px`
              }}
            >
              <svg
                className="absolute inset-0 w-full h-full pointer-events-none"
                style={{ zIndex: 1 }}
              >
                <defs>
                  <marker
                    id="arrowhead"
                    markerWidth="10"
                    markerHeight="7"
                    refX="9"
                    refY="3.5"
                    orient="auto"
                  >
                    <polygon
                      points="0 0, 10 3.5, 0 7"
                      fill="#64748b"
                    />
                  </marker>
                  <filter id="glow">
                    <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                    <feMerge>
                      <feMergeNode in="coloredBlur"/>
                      <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                  </filter>
                </defs>
                <TreeConnections
                  node={treeData.treeStructure}
                  x={0}
                  y={0}
                  depth={0}
                  maxDepth={viewSettings.maxDepth}
                  viewSettings={viewSettings}
                  containerWidth={getResponsiveDimensions().containerWidth}
                  isMobile={isMobile}
                />
              </svg>
              <div className="relative" style={{ zIndex: 2 }}>
                <EnhancedTreeNodeComponent
                  node={treeData.treeStructure}
                  position="ROOT"
                  depth={0}
                  maxDepth={viewSettings.maxDepth}
                  viewSettings={viewSettings}
                  onToggleExpansion={toggleNodeExpansion}
                  onFocusNode={focusOnNode}
                  x={0}
                  y={0}
                  containerWidth={getResponsiveDimensions().containerWidth}
                  isMobile={isMobile}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Referral Links and Binary Matching */}
      <Grid cols={{ default: 1, lg: 2 }} gap={6}>
        {/* Next Binary Matching */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Award className="h-5 w-5 text-solar-500" />
              <span>Next Binary Matching</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <p className="text-sm text-gray-600 mb-4">
                Daily binary matching at 12:00 AM UTC
              </p>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-solar-600">
                    {timeUntilMatching.hours}
                  </div>
                  <div className="text-xs text-gray-500">Hours</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-solar-600">
                    {timeUntilMatching.minutes}
                  </div>
                  <div className="text-xs text-gray-500">Minutes</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-solar-600">
                    {timeUntilMatching.seconds}
                  </div>
                  <div className="text-xs text-gray-500">Seconds</div>
                </div>
              </div>

              <div className="mt-4 p-3 bg-solar-50 rounded-lg">
                <p className="text-sm text-solar-700">
                  <strong>Potential Match:</strong> {Math.min(treeData.statistics?.binaryPoints?.leftPoints || 0, treeData.statistics?.binaryPoints?.rightPoints || 0)} points
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Referral Links */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Share2 className="h-5 w-5 text-eco-500" />
              <span>Referral Links</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">General Referral Link</label>
                <div className="flex mt-1">
                  <input
                    type="text"
                    value={treeData.referralLinks.general}
                    readOnly
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                  />
                  <Button
                    size="sm"
                    onClick={() => handleCopyLink(treeData.referralLinks.general)}
                    className="rounded-l-none"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Left Side Link</label>
                <div className="flex mt-1">
                  <input
                    type="text"
                    value={treeData.referralLinks.left}
                    readOnly
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                  />
                  <Button
                    size="sm"
                    onClick={() => handleCopyLink(treeData.referralLinks.left)}
                    className="rounded-l-none"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Right Side Link</label>
                <div className="flex mt-1">
                  <input
                    type="text"
                    value={treeData.referralLinks.right}
                    readOnly
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                  />
                  <Button
                    size="sm"
                    onClick={() => handleCopyLink(treeData.referralLinks.right)}
                    className="rounded-l-none"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-xs text-blue-700">
                <strong>Enhanced Placement:</strong> New users are automatically placed in your weaker leg
                for optimal network balance. Use specific side links to target placement.
              </p>
            </div>
          </CardContent>
        </Card>
      </Grid>
    </div>
  );
};

// Tree Connections Component for drawing lines
interface TreeConnectionsProps {
  node: EnhancedBinaryTreeNode | null;
  x: number;
  y: number;
  depth: number;
  maxDepth: number;
  viewSettings: TreeViewSettings;
  containerWidth: number;
  isMobile: boolean;
}

const TreeConnections: React.FC<TreeConnectionsProps> = ({
  node,
  x,
  y,
  depth,
  maxDepth,
  viewSettings,
  containerWidth,
  isMobile,
}) => {
  if (!node || depth >= maxDepth) return null;

  // Responsive sizing
  const nodeWidth = isMobile ? 140 : 180;
  const nodeHeight = isMobile ? 120 : 140;
  const verticalSpacing = isMobile ? 140 : 180;
  const levelWidth = containerWidth / Math.pow(2, depth + 1);

  const currentX = containerWidth / 2 + x * levelWidth;
  const currentY = 60 + depth * verticalSpacing;

  const isExpanded = viewSettings.expandedNodes.has(node.user.id);
  const shouldShowChildren = depth < maxDepth - 1 && (isExpanded || depth < 2);

  if (!shouldShowChildren) return null;

  const leftChildX = currentX - levelWidth / 2;
  const rightChildX = currentX + levelWidth / 2;
  const childY = currentY + verticalSpacing;

  return (
    <g>
      {/* Left child connection */}
      {node.leftChild && (viewSettings.showInactive || node.leftChild.user.isActive) && (
        <>
          {/* Curved connection line */}
          <path
            d={`M ${currentX} ${currentY + nodeHeight / 2}
                Q ${currentX - levelWidth / 4} ${currentY + verticalSpacing / 2}
                ${leftChildX} ${childY - 10}`}
            stroke={node.leftChild.user.isActive ? "#10b981" : "#9ca3af"}
            strokeWidth={node.leftChild.user.isActive ? "3" : "2"}
            fill="none"
            strokeDasharray={node.leftChild.user.isActive ? "none" : "5,5"}
            opacity={node.leftChild.user.isActive ? 1 : 0.6}
            filter={node.leftChild.user.isActive ? "url(#glow)" : "none"}
          />
          <circle
            cx={leftChildX}
            cy={childY - 10}
            r="4"
            fill={node.leftChild.user.isActive ? "#10b981" : "#9ca3af"}
            opacity={node.leftChild.user.isActive ? 1 : 0.6}
          />
          {/* Left label */}
          <text
            x={currentX - levelWidth / 4}
            y={currentY + verticalSpacing / 2 - 5}
            textAnchor="middle"
            className="text-xs fill-green-600 font-medium"
            fontSize={isMobile ? "10" : "12"}
          >
            L
          </text>
        </>
      )}

      {/* Right child connection */}
      {node.rightChild && (viewSettings.showInactive || node.rightChild.user.isActive) && (
        <>
          {/* Curved connection line */}
          <path
            d={`M ${currentX} ${currentY + nodeHeight / 2}
                Q ${currentX + levelWidth / 4} ${currentY + verticalSpacing / 2}
                ${rightChildX} ${childY - 10}`}
            stroke={node.rightChild.user.isActive ? "#f59e0b" : "#9ca3af"}
            strokeWidth={node.rightChild.user.isActive ? "3" : "2"}
            fill="none"
            strokeDasharray={node.rightChild.user.isActive ? "none" : "5,5"}
            opacity={node.rightChild.user.isActive ? 1 : 0.6}
            filter={node.rightChild.user.isActive ? "url(#glow)" : "none"}
          />
          <circle
            cx={rightChildX}
            cy={childY - 10}
            r="4"
            fill={node.rightChild.user.isActive ? "#f59e0b" : "#9ca3af"}
            opacity={node.rightChild.user.isActive ? 1 : 0.6}
          />
          {/* Right label */}
          <text
            x={currentX + levelWidth / 4}
            y={currentY + verticalSpacing / 2 - 5}
            textAnchor="middle"
            className="text-xs fill-orange-600 font-medium"
            fontSize={isMobile ? "10" : "12"}
          >
            R
          </text>
        </>
      )}

      {/* Recursive connections for children */}
      {node.leftChild && (viewSettings.showInactive || node.leftChild.user.isActive) && (
        <TreeConnections
          node={node.leftChild}
          x={x * 2 - 1}
          y={y}
          depth={depth + 1}
          maxDepth={maxDepth}
          viewSettings={viewSettings}
          containerWidth={containerWidth}
          isMobile={isMobile}
        />
      )}

      {node.rightChild && (viewSettings.showInactive || node.rightChild.user.isActive) && (
        <TreeConnections
          node={node.rightChild}
          x={x * 2 + 1}
          y={y}
          depth={depth + 1}
          maxDepth={maxDepth}
          viewSettings={viewSettings}
          containerWidth={containerWidth}
          isMobile={isMobile}
        />
      )}
    </g>
  );
};

// Enhanced Tree Node Component with proper positioning
interface EnhancedTreeNodeProps {
  node: EnhancedBinaryTreeNode | null;
  position: string;
  depth: number;
  maxDepth: number;
  viewSettings: TreeViewSettings;
  onToggleExpansion: (nodeId: string) => void;
  onFocusNode: (nodeId: string) => void;
  x: number;
  y: number;
  containerWidth: number;
  isMobile: boolean;
}

const EnhancedTreeNodeComponent: React.FC<EnhancedTreeNodeProps> = ({
  node,
  position,
  depth,
  maxDepth,
  viewSettings,
  onToggleExpansion,
  onFocusNode,
  x,
  y,
  containerWidth,
  isMobile,
}) => {
  if (!node) return null;
  if (!viewSettings.showInactive && !node.user.isActive) return null;

  // Responsive sizing
  const nodeWidth = isMobile ? 140 : 180;
  const nodeHeight = isMobile ? 120 : 140;
  const verticalSpacing = isMobile ? 140 : 180;
  const levelWidth = containerWidth / Math.pow(2, depth + 1);

  const currentX = containerWidth / 2 + x * levelWidth - nodeWidth / 2;
  const currentY = depth * verticalSpacing;

  const isExpanded = viewSettings.expandedNodes.has(node.user.id);
  const isFocused = viewSettings.focusedNode === node.user.id;
  const hasChildren = node.leftChild || node.rightChild;
  const shouldShowChildren = depth < maxDepth - 1 && (isExpanded || depth < 2);

  return (
    <div className="relative">
      {/* Current Node */}
      <div
        className={`absolute bg-white border-2 rounded-xl shadow-lg transition-all duration-300 cursor-pointer hover:shadow-xl hover:scale-102 ${
          isFocused
            ? 'border-blue-500 shadow-blue-200 ring-4 ring-blue-100 scale-105 animate-pulse'
            : node.user.isActive
              ? 'border-emerald-300 hover:border-emerald-400'
              : 'border-gray-300 opacity-75'
        }`}
        style={{
          left: `${currentX}px`,
          top: `${currentY}px`,
          width: `${nodeWidth}px`,
          minHeight: `${nodeHeight}px`,
          animation: depth === 0 ? 'fadeInScale 0.6s ease-out' : `fadeInScale 0.6s ease-out ${depth * 0.1}s both`,
        }}
        onClick={() => onFocusNode(node.user.id)}
      >
        {/* Expansion Toggle */}
        {hasChildren && depth < maxDepth - 1 && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleExpansion(node.user.id);
            }}
            className={`absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-8 h-8 rounded-full flex items-center justify-center text-white shadow-lg transition-all duration-200 ${
              isExpanded
                ? 'bg-red-500 hover:bg-red-600'
                : 'bg-blue-500 hover:bg-blue-600'
            }`}
          >
            {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
          </button>
        )}

        <div className="p-4">
          {/* User Header */}
          <div className="flex items-center space-x-3 mb-3">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-md ${
              node.user.isActive ? 'bg-gradient-to-br from-emerald-400 to-emerald-600' : 'bg-gradient-to-br from-gray-400 to-gray-600'
            }`}>
              {node.user.firstName.charAt(0)}{node.user.lastName.charAt(0)}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-bold text-gray-900 truncate">
                {node.user.firstName} {node.user.lastName}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {node.user.email}
              </p>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 gap-2 mb-3">
            <div className="bg-blue-50 rounded-lg p-2 text-center">
              <div className="text-lg font-bold text-blue-600">{node.teamCounts.total}</div>
              <div className="text-xs text-blue-500">Team</div>
            </div>
            <div className="bg-purple-50 rounded-lg p-2 text-center">
              <div className="text-lg font-bold text-purple-600">{node.directReferralCount}</div>
              <div className="text-xs text-purple-500">Direct</div>
            </div>
          </div>

          {/* Binary Stats */}
          <div className="flex justify-between items-center mb-2">
            <div className="text-center">
              <div className="text-sm font-semibold text-green-600">{node.teamCounts.left}</div>
              <div className="text-xs text-gray-500">Left</div>
            </div>
            <div className="text-xs text-gray-400">|</div>
            <div className="text-center">
              <div className="text-sm font-semibold text-orange-600">{node.teamCounts.right}</div>
              <div className="text-xs text-gray-500">Right</div>
            </div>
          </div>

          {/* Points */}
          <div className="flex justify-between items-center text-xs text-gray-600 mb-2">
            <span>Points: {node.binaryPoints.leftPoints}/{node.binaryPoints.rightPoints}</span>
          </div>

          {/* Status and Date */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-1">
              <Activity className={`h-3 w-3 ${node.user.isActive ? 'text-green-500' : 'text-gray-400'}`} />
              <span className={`text-xs font-medium ${node.user.isActive ? 'text-green-600' : 'text-gray-500'}`}>
                {node.user.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
            <span className="text-xs text-gray-400">
              {formatDate(node.user.createdAt)}
            </span>
          </div>

          {/* Sponsor Badge */}
          {node.sponsorInfo && (
            <div className="mt-2 pt-2 border-t border-gray-100">
              <div className="flex items-center space-x-1">
                <Crown className="h-3 w-3 text-yellow-500" />
                <span className="text-xs text-gray-600 truncate">
                  {node.sponsorInfo.firstName} {node.sponsorInfo.lastName}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Children */}
      {shouldShowChildren && (
        <>
          {node.leftChild && (viewSettings.showInactive || node.leftChild.user.isActive) && (
            <EnhancedTreeNodeComponent
              node={node.leftChild}
              position="Left"
              depth={depth + 1}
              maxDepth={maxDepth}
              viewSettings={viewSettings}
              onToggleExpansion={onToggleExpansion}
              onFocusNode={onFocusNode}
              x={x * 2 - 1}
              y={y}
              containerWidth={containerWidth}
              isMobile={isMobile}
            />
          )}

          {node.rightChild && (viewSettings.showInactive || node.rightChild.user.isActive) && (
            <EnhancedTreeNodeComponent
              node={node.rightChild}
              position="Right"
              depth={depth + 1}
              maxDepth={maxDepth}
              viewSettings={viewSettings}
              onToggleExpansion={onToggleExpansion}
              onFocusNode={onFocusNode}
              x={x * 2 + 1}
              y={y}
              containerWidth={containerWidth}
              isMobile={isMobile}
            />
          )}
        </>
      )}
    </div>
  );
};
