{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(amount);\n}\n\nexport function formatNumber(num: number, decimals = 2): string {\n  return new Intl.NumberFormat('en-US', {\n    minimumFractionDigits: decimals,\n    maximumFractionDigits: decimals,\n  }).format(num);\n}\n\nexport function formatDate(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(d);\n}\n\nexport function formatDateTime(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(d);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function copyToClipboard(text: string): Promise<void> {\n  if (navigator.clipboard) {\n    return navigator.clipboard.writeText(text);\n  }\n  \n  // Fallback for older browsers\n  const textArea = document.createElement('textarea');\n  textArea.value = text;\n  document.body.appendChild(textArea);\n  textArea.focus();\n  textArea.select();\n  \n  try {\n    document.execCommand('copy');\n    return Promise.resolve();\n  } catch (err) {\n    return Promise.reject(err);\n  } finally {\n    document.body.removeChild(textArea);\n  }\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n  \n  if (password.length < 8) {\n    errors.push('Password must be at least 8 characters long');\n  }\n  \n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n  \n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n  \n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n  \n  if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n    errors.push('Password must contain at least one special character');\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n}\n\nexport function calculateROI(\n  investment: number,\n  dailyRate: number,\n  days: number\n): number {\n  return investment * (dailyRate / 100) * days;\n}\n\nexport function calculateTHSPrice(ths: number, pricePerTHS: number): number {\n  return ths * pricePerTHS;\n}\n\nexport function formatTHS(ths: number): string {\n  if (ths >= 1000) {\n    return `${(ths / 1000).toFixed(1)}K TH/s`;\n  }\n  return `${ths.toFixed(2)} TH/s`;\n}\n\nexport function getTimeUntilNextPayout(): {\n  days: number;\n  hours: number;\n  minutes: number;\n  seconds: number;\n} {\n  const now = new Date();\n  const nextSaturday = new Date();\n  \n  // Set to next Saturday at 15:00 UTC\n  nextSaturday.setUTCDate(now.getUTCDate() + (6 - now.getUTCDay()));\n  nextSaturday.setUTCHours(15, 0, 0, 0);\n  \n  // If it's already past Saturday 15:00, move to next week\n  if (now > nextSaturday) {\n    nextSaturday.setUTCDate(nextSaturday.getUTCDate() + 7);\n  }\n  \n  const diff = nextSaturday.getTime() - now.getTime();\n  \n  const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n  const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n  \n  return { days, hours, minutes, seconds };\n}\n\nexport function getTimeUntilBinaryPayout(): {\n  hours: number;\n  minutes: number;\n  seconds: number;\n} {\n  const now = new Date();\n  const nextMidnight = new Date();\n  \n  // Set to next midnight UTC\n  nextMidnight.setUTCDate(now.getUTCDate() + 1);\n  nextMidnight.setUTCHours(0, 0, 0, 0);\n  \n  const diff = nextMidnight.getTime() - now.getTime();\n  \n  const hours = Math.floor(diff / (1000 * 60 * 60));\n  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n  const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n  \n  return { hours, minutes, seconds };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,GAAW,EAAE,WAAW,CAAC;IACpD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,gBAAgB,IAAY;IAC1C,IAAI,UAAU,SAAS,EAAE;QACvB,OAAO,UAAU,SAAS,CAAC,SAAS,CAAC;IACvC;IAEA,8BAA8B;IAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;IACxC,SAAS,KAAK,GAAG;IACjB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,SAAS,KAAK;IACd,SAAS,MAAM;IAEf,IAAI;QACF,SAAS,WAAW,CAAC;QACrB,OAAO,QAAQ,OAAO;IACxB,EAAE,OAAO,KAAK;QACZ,OAAO,QAAQ,MAAM,CAAC;IACxB,SAAU;QACR,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;AACF;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,yBAAyB,IAAI,CAAC,WAAW;QAC5C,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,aACd,UAAkB,EAClB,SAAiB,EACjB,IAAY;IAEZ,OAAO,aAAa,CAAC,YAAY,GAAG,IAAI;AAC1C;AAEO,SAAS,kBAAkB,GAAW,EAAE,WAAmB;IAChE,OAAO,MAAM;AACf;AAEO,SAAS,UAAU,GAAW;IACnC,IAAI,OAAO,MAAM;QACf,OAAO,GAAG,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC;IAC3C;IACA,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,KAAK,CAAC;AACjC;AAEO,SAAS;IAMd,MAAM,MAAM,IAAI;IAChB,MAAM,eAAe,IAAI;IAEzB,oCAAoC;IACpC,aAAa,UAAU,CAAC,IAAI,UAAU,KAAK,CAAC,IAAI,IAAI,SAAS,EAAE;IAC/D,aAAa,WAAW,CAAC,IAAI,GAAG,GAAG;IAEnC,yDAAyD;IACzD,IAAI,MAAM,cAAc;QACtB,aAAa,UAAU,CAAC,aAAa,UAAU,KAAK;IACtD;IAEA,MAAM,OAAO,aAAa,OAAO,KAAK,IAAI,OAAO;IAEjD,MAAM,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;IACnD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE,IAAK,CAAC,OAAO,KAAK,EAAE;IACzE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IACjE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,EAAE,IAAK;IAElD,OAAO;QAAE;QAAM;QAAO;QAAS;IAAQ;AACzC;AAEO,SAAS;IAKd,MAAM,MAAM,IAAI;IAChB,MAAM,eAAe,IAAI;IAEzB,2BAA2B;IAC3B,aAAa,UAAU,CAAC,IAAI,UAAU,KAAK;IAC3C,aAAa,WAAW,CAAC,GAAG,GAAG,GAAG;IAElC,MAAM,OAAO,aAAa,OAAO,KAAK,IAAI,OAAO;IAEjD,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,EAAE;IAC/C,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IACjE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,EAAE,IAAK;IAElD,OAAO;QAAE;QAAO;QAAS;IAAQ;AACnC", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/layout/Container.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ContainerProps {\n  children: React.ReactNode;\n  className?: string;\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';\n}\n\nconst Container: React.FC<ContainerProps> = ({ \n  children, \n  className, \n  size = 'lg' \n}) => {\n  const sizeClasses = {\n    sm: 'max-w-2xl',\n    md: 'max-w-4xl',\n    lg: 'max-w-6xl',\n    xl: 'max-w-7xl',\n    full: 'max-w-full',\n  };\n\n  return (\n    <div\n      className={cn(\n        'mx-auto px-4 sm:px-6 lg:px-8',\n        sizeClasses[size],\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport { Container };\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWA,MAAM,YAAsC,CAAC,EAC3C,QAAQ,EACR,SAAS,EACT,OAAO,IAAI,EACZ;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gCACA,WAAW,CAAC,KAAK,EACjB;kBAGD;;;;;;AAGP;KAxBM", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/layout/Grid.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface GridProps {\n  children: React.ReactNode;\n  className?: string;\n  cols?: {\n    default?: number;\n    sm?: number;\n    md?: number;\n    lg?: number;\n    xl?: number;\n  };\n  gap?: number;\n}\n\nconst Grid: React.FC<GridProps> = ({ \n  children, \n  className, \n  cols = { default: 1, md: 2, lg: 3 },\n  gap = 6\n}) => {\n  const getGridClasses = () => {\n    const classes = ['grid'];\n    \n    // Default columns\n    if (cols.default) {\n      classes.push(`grid-cols-${cols.default}`);\n    }\n    \n    // Responsive columns\n    if (cols.sm) {\n      classes.push(`sm:grid-cols-${cols.sm}`);\n    }\n    if (cols.md) {\n      classes.push(`md:grid-cols-${cols.md}`);\n    }\n    if (cols.lg) {\n      classes.push(`lg:grid-cols-${cols.lg}`);\n    }\n    if (cols.xl) {\n      classes.push(`xl:grid-cols-${cols.xl}`);\n    }\n    \n    // Gap\n    classes.push(`gap-${gap}`);\n    \n    return classes.join(' ');\n  };\n\n  return (\n    <div className={cn(getGridClasses(), className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface GridItemProps {\n  children: React.ReactNode;\n  className?: string;\n  span?: {\n    default?: number;\n    sm?: number;\n    md?: number;\n    lg?: number;\n    xl?: number;\n  };\n}\n\nconst GridItem: React.FC<GridItemProps> = ({ \n  children, \n  className, \n  span \n}) => {\n  const getSpanClasses = () => {\n    if (!span) return '';\n    \n    const classes = [];\n    \n    if (span.default) {\n      classes.push(`col-span-${span.default}`);\n    }\n    if (span.sm) {\n      classes.push(`sm:col-span-${span.sm}`);\n    }\n    if (span.md) {\n      classes.push(`md:col-span-${span.md}`);\n    }\n    if (span.lg) {\n      classes.push(`lg:col-span-${span.lg}`);\n    }\n    if (span.xl) {\n      classes.push(`xl:col-span-${span.xl}`);\n    }\n    \n    return classes.join(' ');\n  };\n\n  return (\n    <div className={cn(getSpanClasses(), className)}>\n      {children}\n    </div>\n  );\n};\n\nexport { Grid, GridItem };\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAkBA,MAAM,OAA4B,CAAC,EACjC,QAAQ,EACR,SAAS,EACT,OAAO;IAAE,SAAS;IAAG,IAAI;IAAG,IAAI;AAAE,CAAC,EACnC,MAAM,CAAC,EACR;IACC,MAAM,iBAAiB;QACrB,MAAM,UAAU;YAAC;SAAO;QAExB,kBAAkB;QAClB,IAAI,KAAK,OAAO,EAAE;YAChB,QAAQ,IAAI,CAAC,CAAC,UAAU,EAAE,KAAK,OAAO,EAAE;QAC1C;QAEA,qBAAqB;QACrB,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACxC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACxC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACxC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACxC;QAEA,MAAM;QACN,QAAQ,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK;QAEzB,OAAO,QAAQ,IAAI,CAAC;IACtB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAClC;;;;;;AAGP;KAvCM;AAqDN,MAAM,WAAoC,CAAC,EACzC,QAAQ,EACR,SAAS,EACT,IAAI,EACL;IACC,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,UAAU,EAAE;QAElB,IAAI,KAAK,OAAO,EAAE;YAChB,QAAQ,IAAI,CAAC,CAAC,SAAS,EAAE,KAAK,OAAO,EAAE;QACzC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;QACvC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;QACvC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;QACvC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;QACvC;QAEA,OAAO,QAAQ,IAAI,CAAC;IACtB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAClC;;;;;;AAGP;MAlCM", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/layout/Flex.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface FlexProps {\n  children: React.ReactNode;\n  className?: string;\n  direction?: 'row' | 'col' | 'row-reverse' | 'col-reverse';\n  align?: 'start' | 'center' | 'end' | 'stretch' | 'baseline';\n  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';\n  wrap?: 'wrap' | 'nowrap' | 'wrap-reverse';\n  gap?: number;\n}\n\nconst Flex: React.FC<FlexProps> = ({\n  children,\n  className,\n  direction = 'row',\n  align = 'start',\n  justify = 'start',\n  wrap = 'nowrap',\n  gap = 0,\n}) => {\n  const directionClasses = {\n    row: 'flex-row',\n    col: 'flex-col',\n    'row-reverse': 'flex-row-reverse',\n    'col-reverse': 'flex-col-reverse',\n  };\n\n  const alignClasses = {\n    start: 'items-start',\n    center: 'items-center',\n    end: 'items-end',\n    stretch: 'items-stretch',\n    baseline: 'items-baseline',\n  };\n\n  const justifyClasses = {\n    start: 'justify-start',\n    center: 'justify-center',\n    end: 'justify-end',\n    between: 'justify-between',\n    around: 'justify-around',\n    evenly: 'justify-evenly',\n  };\n\n  const wrapClasses = {\n    wrap: 'flex-wrap',\n    nowrap: 'flex-nowrap',\n    'wrap-reverse': 'flex-wrap-reverse',\n  };\n\n  return (\n    <div\n      className={cn(\n        'flex',\n        directionClasses[direction],\n        alignClasses[align],\n        justifyClasses[justify],\n        wrapClasses[wrap],\n        gap > 0 && `gap-${gap}`,\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport { Flex };\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAeA,MAAM,OAA4B,CAAC,EACjC,QAAQ,EACR,SAAS,EACT,YAAY,KAAK,EACjB,QAAQ,OAAO,EACf,UAAU,OAAO,EACjB,OAAO,QAAQ,EACf,MAAM,CAAC,EACR;IACC,MAAM,mBAAmB;QACvB,KAAK;QACL,KAAK;QACL,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,eAAe;QACnB,OAAO;QACP,QAAQ;QACR,KAAK;QACL,SAAS;QACT,UAAU;IACZ;IAEA,MAAM,iBAAiB;QACrB,OAAO;QACP,QAAQ;QACR,KAAK;QACL,SAAS;QACT,QAAQ;QACR,QAAQ;IACV;IAEA,MAAM,cAAc;QAClB,MAAM;QACN,QAAQ;QACR,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,QACA,gBAAgB,CAAC,UAAU,EAC3B,YAAY,CAAC,MAAM,EACnB,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB,MAAM,KAAK,CAAC,IAAI,EAAE,KAAK,EACvB;kBAGD;;;;;;AAGP;KAtDM", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-xl font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl',\n  {\n    variants: {\n      variant: {\n        primary: 'bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-500',\n        secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-gray-500',\n        success: 'bg-emerald-500 text-white hover:bg-emerald-600 focus:ring-emerald-500',\n        danger: 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500',\n        outline: 'border-2 border-yellow-500 bg-transparent text-yellow-600 hover:bg-yellow-500 hover:text-white focus:ring-yellow-500',\n        ghost: 'text-gray-600 hover:bg-yellow-50 hover:text-yellow-700 focus:ring-yellow-500 rounded-lg',\n        link: 'text-yellow-600 underline-offset-4 hover:underline focus:ring-yellow-500 hover:text-yellow-700',\n        premium: 'bg-slate-800 text-white hover:bg-slate-900 focus:ring-slate-500',\n        glass: 'glass-morphism text-slate-900 hover:bg-white/20 backdrop-blur-xl border border-white/20',\n      },\n      size: {\n        sm: 'h-10 px-4 text-sm rounded-lg',\n        md: 'h-12 px-6 text-base rounded-xl',\n        lg: 'h-14 px-8 text-lg rounded-xl',\n        xl: 'h-16 px-10 text-xl rounded-2xl font-bold',\n        icon: 'h-12 w-12 rounded-xl',\n      },\n    },\n    defaultVariants: {\n      variant: 'primary',\n      size: 'md',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, leftIcon, rightIcon, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <div className=\"mr-2\">\n            <div className=\"spinner\" />\n          </div>\n        )}\n        {leftIcon && !loading && <span className=\"mr-2\">{leftIcon}</span>}\n        {children}\n        {rightIcon && !loading && <span className=\"ml-2\">{rightIcon}</span>}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,yQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,SAAS;YACT,QAAQ;YACR,SAAS;YACT,OAAO;YACP,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAWF,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;YAGlB,YAAY,CAAC,yBAAW,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;YAChD;YACA,aAAa,CAAC,yBAAW,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAGxD;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          'rounded-2xl border border-gray-200/50 bg-white/90 backdrop-blur-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02] overflow-hidden',\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCard.displayName = 'Card';\n\nexport interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('flex flex-col space-y-1.5 p-6 pb-4', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCardHeader.displayName = 'CardHeader';\n\nexport interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {\n  children: React.ReactNode;\n}\n\nconst CardTitle = React.forwardRef<HTMLParagraphElement, CardTitleProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <h3\n        ref={ref}\n        className={cn('text-xl font-semibold leading-none tracking-tight text-dark-900', className)}\n        {...props}\n      >\n        {children}\n      </h3>\n    );\n  }\n);\n\nCardTitle.displayName = 'CardTitle';\n\nexport interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {\n  children: React.ReactNode;\n}\n\nconst CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <p\n        ref={ref}\n        className={cn('text-sm text-gray-500', className)}\n        {...props}\n      >\n        {children}\n      </p>\n    );\n  }\n);\n\nCardDescription.displayName = 'CardDescription';\n\nexport interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('p-6 pt-0', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCardContent.displayName = 'CardContent';\n\nexport interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('flex items-center p-6 pt-0', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AASA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0KACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,KAAK,WAAW,GAAG;AAMnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,WAAW,WAAW,GAAG;AAMzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAChC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,UAAU,WAAW,GAAG;AAMxB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OACtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,gBAAgB,WAAW,GAAG;AAM9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,YAAY,WAAW,GAAG;AAM1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 610, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Input.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, leftIcon, rightIcon, ...props }, ref) => {\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <span className=\"text-gray-400\">{leftIcon}</span>\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              'flex h-14 w-full rounded-xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm px-4 py-4 text-base shadow-inner focus:shadow-lg placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-solar-500 focus:border-solar-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 hover:border-gray-300',\n              leftIcon && 'pl-12',\n              rightIcon && 'pr-12',\n              error && 'border-red-500 focus:ring-red-500 focus:border-red-500',\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n          {rightIcon && (\n            <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n              <span className=\"text-gray-400\">{rightIcon}</span>\n            </div>\n          )}\n        </div>\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYA,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACjE,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAiB;;;;;;;;;;;kCAGrC,6LAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8UACA,YAAY,SACZ,aAAa,SACb,SAAS,0DACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;oBAEV,2BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAiB;;;;;;;;;;;;;;;;;YAItC,uBACC,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 711, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Modal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { createPortal } from 'react-dom';\nimport { X } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Button } from './Button';\n\nexport interface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: React.ReactNode;\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  showCloseButton?: boolean;\n}\n\nconst Modal: React.FC<ModalProps> = ({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'md',\n  showCloseButton = true,\n}) => {\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  useEffect(() => {\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const sizeClasses = {\n    sm: 'max-w-md',\n    md: 'max-w-lg',\n    lg: 'max-w-2xl',\n    xl: 'max-w-4xl',\n  };\n\n  const modalContent = (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\n      {/* Backdrop */}\n      <div\n        className=\"fixed inset-0 bg-black bg-opacity-50 transition-opacity\"\n        onClick={onClose}\n      />\n      \n      {/* Modal */}\n      <div\n        className={cn(\n          'relative w-full bg-white rounded-xl shadow-xl transform transition-all',\n          sizeClasses[size]\n        )}\n        onClick={(e) => e.stopPropagation()}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <h2 className=\"text-xl font-semibold text-dark-900\">{title}</h2>\n          {showCloseButton && (\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onClose}\n              className=\"h-8 w-8 rounded-full\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          )}\n        </div>\n        \n        {/* Content */}\n        <div className=\"p-6\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n\n  return createPortal(modalContent, document.body);\n};\n\nexport { Modal };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAiBA,MAAM,QAA8B,CAAC,EACnC,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACX,kBAAkB,IAAI,EACvB;;IACC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,QAAQ;gBACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,MAAM;gDAAe,CAAC;oBACpB,IAAI,MAAM,GAAG,KAAK,UAAU;wBAC1B;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,WAAW;YACvC;YAEA;mCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;gBAC1C;;QACF;0BAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,6BACJ,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA,WAAW,CAAC,KAAK;gBAEnB,SAAS,CAAC,IAAM,EAAE,eAAe;;kCAGjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;4BACpD,iCACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAMnB,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;IAMT,qBAAO,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,cAAc,SAAS,IAAI;AACjD;GArFM;KAAA", "debugId": null}}, {"offset": {"line": 860, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Loading.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface LoadingProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n  text?: string;\n}\n\nconst Loading: React.FC<LoadingProps> = ({ size = 'md', className, text }) => {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12',\n  };\n\n  return (\n    <div className={cn('flex flex-col items-center justify-center', className)}>\n      <div\n        className={cn(\n          'animate-spin rounded-full border-2 border-gray-300 border-t-solar-500',\n          sizeClasses[size]\n        )}\n      />\n      {text && (\n        <p className=\"mt-2 text-sm text-gray-600\">{text}</p>\n      )}\n    </div>\n  );\n};\n\nexport interface LoadingOverlayProps {\n  isLoading: boolean;\n  text?: string;\n  children: React.ReactNode;\n}\n\nconst LoadingOverlay: React.FC<LoadingOverlayProps> = ({\n  isLoading,\n  text = 'Loading...',\n  children,\n}) => {\n  return (\n    <div className=\"relative\">\n      {children}\n      {isLoading && (\n        <div className=\"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10\">\n          <Loading text={text} />\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport { Loading, LoadingOverlay };\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAWA,MAAM,UAAkC,CAAC,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE;IACvE,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;;0BAC9D,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yEACA,WAAW,CAAC,KAAK;;;;;;YAGpB,sBACC,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;KApBM;AA4BN,MAAM,iBAAgD,CAAC,EACrD,SAAS,EACT,OAAO,YAAY,EACnB,QAAQ,EACT;IACC,qBACE,6LAAC;QAAI,WAAU;;YACZ;YACA,2BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAQ,MAAM;;;;;;;;;;;;;;;;;AAKzB;MAfM", "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/index.ts"], "sourcesContent": ["export { Button, buttonVariants, type ButtonProps } from './Button';\nexport { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from './Card';\nexport { Input, type InputProps } from './Input';\nexport { Modal, type ModalProps } from './Modal';\nexport { Loading, LoadingOverlay, type LoadingProps } from './Loading';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 974, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/SolarPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const SolarPanel: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <rect x=\"2\" y=\"4\" width=\"20\" height=\"16\" rx=\"2\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <line x1=\"2\" y1=\"8\" x2=\"22\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"2\" y1=\"16\" x2=\"22\" y2=\"16\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"6\" y1=\"4\" x2=\"6\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"10\" y1=\"4\" x2=\"10\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"14\" y1=\"4\" x2=\"14\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"18\" y1=\"4\" x2=\"18\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <circle cx=\"20\" cy=\"2\" r=\"1\" fill=\"currentColor\"/>\n      <path d=\"M19 1l1 1-1 1-1-1z\" fill=\"currentColor\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;;AASO,MAAM,aAAkC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACtE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,6LAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACrE,6LAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,6LAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,6LAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACrE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAI,GAAE;gBAAI,MAAK;;;;;;0BAClC,6LAAC;gBAAK,GAAE;gBAAqB,MAAK;;;;;;;;;;;;AAGxC;KAtBa", "debugId": null}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/MiningRig.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const MiningRig: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <rect x=\"2\" y=\"6\" width=\"20\" height=\"12\" rx=\"2\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <rect x=\"4\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"8\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"12\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"16\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"4\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"8\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"12\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"16\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <circle cx=\"20\" cy=\"4\" r=\"1\" fill=\"currentColor\"/>\n      <line x1=\"20\" y1=\"4\" x2=\"20\" y2=\"6\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"18\" y1=\"2\" x2=\"22\" y2=\"2\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"19\" y1=\"1\" x2=\"21\" y2=\"3\" stroke=\"currentColor\" strokeWidth=\"1\"/>\n      <line x1=\"21\" y1=\"1\" x2=\"19\" y2=\"3\" stroke=\"currentColor\" strokeWidth=\"1\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;;AASO,MAAM,YAAiC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACrE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACrD,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACrD,6LAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,6LAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,6LAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACvD,6LAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACvD,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAI,GAAE;gBAAI,MAAK;;;;;;0BAClC,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG5E;KA1Ba", "debugId": null}}, {"offset": {"line": 1326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/Cryptocurrency.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const Cryptocurrency: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8 12h8M12 8v8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <path d=\"M10 8h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M10 14h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <line x1=\"12\" y1=\"6\" x2=\"12\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"12\" y1=\"16\" x2=\"12\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n    </svg>\n  );\n};\n\nexport const Bitcoin: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8 12h4c1.1 0 2-.9 2-2s-.9-2-2-2H8v8h4c1.1 0 2-.9 2-2s-.9-2-2-2\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <line x1=\"10\" y1=\"6\" x2=\"10\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"10\" y1=\"16\" x2=\"10\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"14\" y1=\"6\" x2=\"14\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"14\" y1=\"16\" x2=\"14\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AASO,MAAM,iBAAsC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IAC1E,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAK,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1E,6LAAC;gBAAK,GAAE;gBAAiB,QAAO;gBAAe,aAAY;;;;;;0BAC3D,6LAAC;gBAAK,GAAE;gBAAsC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACzF,6LAAC;gBAAK,GAAE;gBAAuC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1F,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG9E;KAlBa;AAoBN,MAAM,UAA+B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACnE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAK,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1E,6LAAC;gBAAK,GAAE;gBAAkE,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACrH,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACxE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG9E;MAlBa", "debugId": null}}, {"offset": {"line": 1514, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/EcoFriendly.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const Leaf: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <path d=\"M12 2C8 2 5 5 5 9c0 5 7 13 7 13s7-8 7-13c0-4-3-7-7-7z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M12 9c0-2-1-3-3-3s-3 1-3 3 1 3 3 3 3-1 3-3z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8 9c0 1 1 2 2 2s2-1 2-2-1-2-2-2-2 1-2 2z\" fill=\"currentColor\"/>\n    </svg>\n  );\n};\n\nexport const Recycle: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <path d=\"M7 19H4.815a1.83 1.83 0 01-1.57-.881 1.785 1.785 0 01-.004-1.784L7.196 9.5\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M11 19h8.203a1.83 1.83 0 001.556-.89 1.784 1.784 0 000-1.775l-1.226-2.12\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M14 16l-3 3 3 3\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8.293 13.596L7.196 9.5l3.1 1.598\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M9.344 5.811L11.271 2a1.784 1.784 0 011.57-.881c.65 0 1.235.361 1.556.881l3.68 6.361\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M16 8l3-3-3-3\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n    </svg>\n  );\n};\n\nexport const WindTurbine: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <line x1=\"12\" y1=\"12\" x2=\"12\" y2=\"22\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <path d=\"M12 12L8 4c-1-2 0-4 2-4s3 2 2 4l-2 8z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M12 12l8-4c2-1 4 0 4 2s-2 3-4 2l-8-2z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M12 12l-4 8c-1 2-3 2-4 0s0-3 2-4l8-2z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <circle cx=\"12\" cy=\"12\" r=\"1\" fill=\"currentColor\"/>\n      <line x1=\"10\" y1=\"22\" x2=\"14\" y2=\"22\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;AASO,MAAM,OAA4B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IAChE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAK,GAAE;gBAAwD,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3G,6LAAC;gBAAK,GAAE;gBAA8C,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACjG,6LAAC;gBAAK,GAAE;gBAA4C,MAAK;;;;;;;;;;;;AAG/D;KAfa;AAiBN,MAAM,UAA+B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACnE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAK,GAAE;gBAA6E,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAChI,6LAAC;gBAAK,GAAE;gBAA2E,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC9H,6LAAC;gBAAK,GAAE;gBAAkB,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACrE,6LAAC;gBAAK,GAAE;gBAAoC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACvF,6LAAC;gBAAK,GAAE;gBAAuF,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1I,6LAAC;gBAAK,GAAE;gBAAgB,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;;;;;;;AAGzE;MAlBa;AAoBN,MAAM,cAAmC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACvE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACxE,6LAAC;gBAAK,GAAE;gBAAwC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,6LAAC;gBAAK,GAAE;gBAAwC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,6LAAC;gBAAK,GAAE;gBAAwC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAI,MAAK;;;;;;0BACnC,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG9E;MAlBa", "debugId": null}}, {"offset": {"line": 1738, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/index.ts"], "sourcesContent": ["export { SolarPanel } from './SolarPanel';\nexport { MiningRig } from './MiningRig';\nexport { Cryptocurrency, Bitcoin } from './Cryptocurrency';\nexport { Leaf, Recycle, WindTurbine } from './EcoFriendly';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1768, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/layout/PublicLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Button } from '@/components/ui';\nimport { Container, Grid, Flex } from '@/components/layout';\nimport { SolarPanel, Leaf } from '@/components/icons';\n\ninterface PublicLayoutProps {\n  children: React.ReactNode;\n}\n\nexport const PublicLayout: React.FC<PublicLayoutProps> = ({ children }) => {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Premium Navigation */}\n      <nav className=\"fixed top-0 w-full z-50 glass-morphism border-b border-white/20\">\n        <Container>\n          <Flex justify=\"between\" align=\"center\" className=\"h-20\">\n            <Link href=\"/\" className=\"flex items-center space-x-3\">\n              <div className=\"relative\">\n                <SolarPanel className=\"h-10 w-10 text-yellow-500 animate-pulse\" />\n                <div className=\"absolute inset-0 bg-yellow-500/20 rounded-full animate-ping\"></div>\n              </div>\n              <span className=\"text-3xl font-black text-slate-900\">\n                HashCoreX\n              </span>\n            </Link>\n            <Flex align=\"center\" gap={8}>\n              <Link href=\"/about\" className=\"text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium hover:scale-105\">\n                About\n              </Link>\n              <Link href=\"/how-it-works\" className=\"text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium hover:scale-105\">\n                How It Works\n              </Link>\n              <Link href=\"/login\">\n                <Button variant=\"ghost\" size=\"md\" className=\"font-semibold\">\n                  Login\n                </Button>\n              </Link>\n              <Link href=\"/register\">\n                <Button variant=\"primary\" size=\"md\" className=\"font-semibold\">\n                  Get Started\n                </Button>\n              </Link>\n            </Flex>\n          </Flex>\n        </Container>\n      </nav>\n\n      {/* Main Content */}\n      <main className=\"pt-20\">\n        {children}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-dark-900 text-white py-12\">\n        <Container>\n          <Grid cols={{ default: 1, md: 4 }} gap={8}>\n            <div>\n              <Flex align=\"center\" gap={2} className=\"mb-4\">\n                <SolarPanel className=\"h-8 w-8 text-yellow-400\" />\n                <span className=\"text-2xl font-bold\">HashCoreX</span>\n              </Flex>\n              <p className=\"text-gray-400\">\n                Sustainable cryptocurrency mining powered by renewable energy.\n              </p>\n            </div>\n            <div>\n              <h4 className=\"font-semibold mb-4\">Platform</h4>\n              <div className=\"space-y-2\">\n                <Link href=\"/about\" className=\"block text-gray-400 hover:text-white transition-colors\">\n                  About Us\n                </Link>\n                <Link href=\"/how-it-works\" className=\"block text-gray-400 hover:text-white transition-colors\">\n                  How It Works\n                </Link>\n                <Link href=\"/pricing\" className=\"block text-gray-400 hover:text-white transition-colors\">\n                  Pricing\n                </Link>\n              </div>\n            </div>\n            <div>\n              <h4 className=\"font-semibold mb-4\">Support</h4>\n              <div className=\"space-y-2\">\n                <Link href=\"/contact\" className=\"block text-gray-400 hover:text-white transition-colors\">\n                  Contact Us\n                </Link>\n                <Link href=\"/faq\" className=\"block text-gray-400 hover:text-white transition-colors\">\n                  FAQ\n                </Link>\n                <Link href=\"/help\" className=\"block text-gray-400 hover:text-white transition-colors\">\n                  Help Center\n                </Link>\n              </div>\n            </div>\n            <div>\n              <h4 className=\"font-semibold mb-4\">Legal</h4>\n              <div className=\"space-y-2\">\n                <Link href=\"/privacy\" className=\"block text-gray-400 hover:text-white transition-colors\">\n                  Privacy Policy\n                </Link>\n                <Link href=\"/terms\" className=\"block text-gray-400 hover:text-white transition-colors\">\n                  Terms of Service\n                </Link>\n                <Link href=\"/compliance\" className=\"block text-gray-400 hover:text-white transition-colors\">\n                  Compliance\n                </Link>\n              </div>\n            </div>\n          </Grid>\n          <div className=\"border-t border-gray-800 mt-12 pt-8 text-center text-gray-400\">\n            <p>&copy; 2024 HashCoreX. All rights reserved.</p>\n          </div>\n        </Container>\n      </footer>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AANA;;;;;;AAYO,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IACpE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,4IAAA,CAAA,YAAS;8BACR,cAAA,6LAAC,uIAAA,CAAA,OAAI;wBAAC,SAAQ;wBAAU,OAAM;wBAAS,WAAU;;0CAC/C,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,4IAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,6LAAC;wCAAK,WAAU;kDAAqC;;;;;;;;;;;;0CAIvD,6LAAC,uIAAA,CAAA,OAAI;gCAAC,OAAM;gCAAS,KAAK;;kDACxB,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAA8F;;;;;;kDAG5H,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAgB,WAAU;kDAA8F;;;;;;kDAGnI,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAI9D,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUxE,6LAAC;gBAAK,WAAU;0BACb;;;;;;0BAIH,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC,4IAAA,CAAA,YAAS;;sCACR,6LAAC,uIAAA,CAAA,OAAI;4BAAC,MAAM;gCAAE,SAAS;gCAAG,IAAI;4BAAE;4BAAG,KAAK;;8CACtC,6LAAC;;sDACC,6LAAC,uIAAA,CAAA,OAAI;4CAAC,OAAM;4CAAS,KAAK;4CAAG,WAAU;;8DACrC,6LAAC,4IAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC;oDAAK,WAAU;8DAAqB;;;;;;;;;;;;sDAEvC,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;8DAAyD;;;;;;8DAGvF,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,WAAU;8DAAyD;;;;;;8DAG9F,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;8DAAyD;;;;;;;;;;;;;;;;;;8CAK7F,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;8DAAyD;;;;;;8DAGzF,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAO,WAAU;8DAAyD;;;;;;8DAGrF,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;8DAAyD;;;;;;;;;;;;;;;;;;8CAK1F,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;8DAAyD;;;;;;8DAGzF,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;8DAAyD;;;;;;8DAGvF,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAc,WAAU;8DAAyD;;;;;;;;;;;;;;;;;;;;;;;;sCAMlG,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf;KA1Ga", "debugId": null}}, {"offset": {"line": 2185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/layout/index.ts"], "sourcesContent": ["export { Container } from './Container';\nexport { Grid, GridItem } from './Grid';\nexport { Flex } from './Flex';\nexport { PublicLayout } from './PublicLayout';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/hooks/useAuth';\nimport { Container, Flex } from '@/components/layout';\nimport { Button } from '@/components/ui';\nimport { SolarPanel } from '@/components/icons';\nimport {\n  LayoutDashboard,\n  Users,\n  Shield,\n  CreditCard,\n  Settings,\n  FileText,\n  LogOut,\n  Menu,\n  X,\n  Bell,\n  ChevronDown,\n  User,\n  ArrowLeft\n} from 'lucide-react';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  activeTab: string;\n  onTabChange: (tab: string) => void;\n}\n\nexport const AdminLayout: React.FC<AdminLayoutProps> = ({\n  children,\n  activeTab,\n  onTabChange,\n}) => {\n  const { user, logout } = useAuth();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [userDropdownOpen, setUserDropdownOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // Admin navigation items\n  const navigationItems = [\n    { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard },\n    { id: 'users', label: 'User Management', icon: Users },\n    { id: 'kyc', label: 'KYC Review', icon: Shield },\n    { id: 'withdrawals', label: 'Withdrawals', icon: CreditCard },\n    { id: 'settings', label: 'System Settings', icon: Settings },\n    { id: 'logs', label: 'System Logs', icon: FileText },\n  ];\n\n  const handleLogout = async () => {\n    await logout();\n  };\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setUserDropdownOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-slate-900 flex\">\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div\n          className=\"fixed inset-0 z-40 bg-black bg-opacity-75 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Fixed Sidebar */}\n      <aside className={`\n        fixed inset-y-0 left-0 z-50 w-64 bg-slate-800 shadow-xl border-r border-slate-700\n        transform transition-all duration-300 ease-in-out\n        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\n      `}>\n        <div className=\"flex flex-col h-screen\">\n          {/* Logo Header */}\n          <div className=\"flex items-center justify-between h-14 px-5 border-b border-slate-700 bg-slate-800 flex-shrink-0\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <SolarPanel className=\"h-5 w-5 text-white\" />\n              </div>\n              <span className=\"text-lg font-bold text-white\">HashCoreX</span>\n            </Link>\n            <button\n              onClick={() => setSidebarOpen(false)}\n              className=\"lg:hidden p-1.5 rounded-lg text-slate-400 hover:text-white hover:bg-slate-700 transition-colors\"\n            >\n              <X className=\"h-4 w-4\" />\n            </button>\n          </div>\n\n          {/* Admin Badge */}\n          <div className=\"px-3 py-3 bg-red-600 border-b border-slate-700\">\n            <div className=\"flex items-center space-x-2 text-white\">\n              <Shield className=\"h-4 w-4\" />\n              <span className=\"text-sm font-semibold\">Admin Panel</span>\n            </div>\n          </div>\n\n          {/* Navigation Menu */}\n          <nav className=\"flex-1 px-3 py-4 space-y-1 min-h-0\">\n            {navigationItems.map((item) => {\n              const Icon = item.icon;\n              const isActive = activeTab === item.id;\n\n              return (\n                <button\n                  key={item.id}\n                  onClick={() => {\n                    onTabChange(item.id);\n                    setSidebarOpen(false);\n                  }}\n                  className={`\n                    w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200 group\n                    ${isActive\n                      ? 'bg-blue-600 text-white shadow-md'\n                      : 'text-slate-300 hover:bg-slate-700 hover:text-white'\n                    }\n                  `}\n                >\n                  <Icon className={`h-4 w-4 ${isActive ? 'text-white' : 'text-slate-400 group-hover:text-white'}`} />\n                  <span className=\"font-medium text-sm\">{item.label}</span>\n                </button>\n              );\n            })}\n          </nav>\n\n          {/* Back to Dashboard */}\n          <div className=\"px-3 py-3 border-t border-slate-700\">\n            <Link\n              href=\"/dashboard\"\n              className=\"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-slate-300 hover:bg-orange-600 hover:text-white transition-all duration-200 group\"\n            >\n              <ArrowLeft className=\"h-4 w-4 group-hover:text-white\" />\n              <span className=\"font-medium text-sm\">Back to Dashboard</span>\n            </Link>\n          </div>\n\n          {/* Sidebar Footer */}\n          <div className=\"px-3 py-3 border-t border-slate-700 bg-slate-900 flex-shrink-0\">\n            <button\n              onClick={handleLogout}\n              className=\"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-slate-300 hover:bg-red-600 hover:text-white transition-all duration-200 group\"\n            >\n              <LogOut className=\"h-4 w-4 group-hover:text-white\" />\n              <span className=\"font-medium text-sm\">Logout</span>\n            </button>\n          </div>\n        </div>\n      </aside>\n\n      {/* Main Content Area */}\n      <div className=\"flex-1 flex flex-col min-w-0 lg:ml-64\">\n        {/* Top Navigation Bar */}\n        <header className=\"bg-slate-800 shadow-sm border-b border-slate-700 sticky top-0 z-30\">\n          <div className=\"px-4 sm:px-6 lg:px-8 xl:px-12\">\n            <Flex justify=\"between\" align=\"center\" className=\"h-16\">\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => setSidebarOpen(true)}\n                  className=\"lg:hidden p-2 rounded-lg text-slate-400 hover:text-white hover:bg-slate-700 transition-colors\"\n                >\n                  <Menu className=\"h-6 w-6\" />\n                </button>\n                <div>\n                  <h1 className=\"text-xl font-bold text-white capitalize\">\n                    {navigationItems.find(item => item.id === activeTab)?.label || 'Admin Dashboard'}\n                  </h1>\n                  <p className=\"text-sm text-slate-400 hidden sm:block\">\n                    Manage platform operations and user activities\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3\">\n                {/* Notifications */}\n                <button className=\"relative p-2 rounded-lg text-slate-400 hover:text-white hover:bg-slate-700 transition-colors\">\n                  <Bell className=\"h-5 w-5\" />\n                  <span className=\"absolute top-1 right-1 h-2 w-2 bg-orange-500 rounded-full\"></span>\n                </button>\n\n                {/* Admin Badge */}\n                <div className=\"px-3 py-1.5 rounded-lg text-xs font-semibold border bg-red-600 text-white border-red-500\">\n                  ADMIN\n                </div>\n\n                {/* User Dropdown */}\n                <div className=\"relative\" ref={dropdownRef}>\n                  <button\n                    onClick={() => setUserDropdownOpen(!userDropdownOpen)}\n                    className=\"flex items-center space-x-2 p-1 rounded-lg hover:bg-slate-700 transition-colors\"\n                  >\n                    <div className=\"w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-semibold text-sm\">\n                        {user?.firstName?.charAt(0).toUpperCase() || user?.email.charAt(0).toUpperCase()}\n                      </span>\n                    </div>\n                    <ChevronDown className={`h-4 w-4 text-slate-400 transition-transform ${userDropdownOpen ? 'rotate-180' : ''}`} />\n                  </button>\n\n                  {/* Dropdown Menu */}\n                  {userDropdownOpen && (\n                    <div className=\"absolute right-0 mt-2 w-64 bg-slate-800 rounded-xl shadow-lg border border-slate-700 py-2 z-50\">\n                      {/* User Info */}\n                      <div className=\"px-4 py-3 border-b border-slate-700\">\n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center\">\n                            <span className=\"text-white font-bold\">\n                              {user?.firstName?.charAt(0).toUpperCase() || user?.email.charAt(0).toUpperCase()}\n                            </span>\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <p className=\"text-sm font-semibold text-white truncate\">\n                              {user?.firstName && user?.lastName\n                                ? `${user.firstName} ${user.lastName}`\n                                : user?.email.split('@')[0]\n                              }\n                            </p>\n                            <p className=\"text-xs text-slate-400\">\n                              ID: {user?.referralId}\n                            </p>\n                            <p className=\"text-xs text-red-400 font-medium\">\n                              Administrator\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Menu Items */}\n                      <div className=\"py-1\">\n                        <Link\n                          href=\"/dashboard\"\n                          onClick={() => setUserDropdownOpen(false)}\n                          className=\"w-full flex items-center space-x-3 px-4 py-2 text-sm text-slate-300 hover:bg-slate-700 transition-colors\"\n                        >\n                          <User className=\"h-4 w-4\" />\n                          <span>User Dashboard</span>\n                        </Link>\n                        <div className=\"border-t border-slate-700 my-1\"></div>\n                        <button\n                          onClick={() => {\n                            setUserDropdownOpen(false);\n                            handleLogout();\n                          }}\n                          className=\"w-full flex items-center space-x-3 px-4 py-2 text-sm text-red-400 hover:bg-red-600 hover:text-white transition-colors\"\n                        >\n                          <LogOut className=\"h-4 w-4\" />\n                          <span>Sign Out</span>\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </Flex>\n          </div>\n        </header>\n\n        {/* Main Content */}\n        <main className=\"flex-1 bg-slate-900 overflow-y-auto\">\n          <div className=\"px-4 sm:px-6 lg:px-8 xl:px-12 py-6\">\n            <div className=\"max-w-7xl mx-auto\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;AA8BO,MAAM,cAA0C,CAAC,EACtD,QAAQ,EACR,SAAS,EACT,WAAW,EACZ;;IACC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,yBAAyB;IACzB,MAAM,kBAAkB;QACtB;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,+NAAA,CAAA,kBAAe;QAAC;QAC7D;YAAE,IAAI;YAAS,OAAO;YAAmB,MAAM,uMAAA,CAAA,QAAK;QAAC;QACrD;YAAE,IAAI;YAAO,OAAO;YAAc,MAAM,yMAAA,CAAA,SAAM;QAAC;QAC/C;YAAE,IAAI;YAAe,OAAO;YAAe,MAAM,qNAAA,CAAA,aAAU;QAAC;QAC5D;YAAE,IAAI;YAAY,OAAO;YAAmB,MAAM,6MAAA,CAAA,WAAQ;QAAC;QAC3D;YAAE,IAAI;YAAQ,OAAO;YAAe,MAAM,iNAAA,CAAA,WAAQ;QAAC;KACpD;IAED,MAAM,eAAe;QACnB,MAAM;IACR;IAEA,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;4DAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E,oBAAoB;oBACtB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;yCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;gCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;YAEZ,6BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,6LAAC;gBAAM,WAAW,CAAC;;;QAGjB,EAAE,cAAc,kBAAkB,qCAAqC;MACzE,CAAC;0BACC,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,4IAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;8CAEjD,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAK5C,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC;gCACpB,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,cAAc,KAAK,EAAE;gCAEtC,qBACE,6LAAC;oCAEC,SAAS;wCACP,YAAY,KAAK,EAAE;wCACnB,eAAe;oCACjB;oCACA,WAAW,CAAC;;oBAEV,EAAE,WACE,qCACA,qDACH;kBACH,CAAC;;sDAED,6LAAC;4CAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,eAAe,yCAAyC;;;;;;sDAC/F,6LAAC;4CAAK,WAAU;sDAAuB,KAAK,KAAK;;;;;;;mCAd5C,KAAK,EAAE;;;;;4BAiBlB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;;;;;;sCAK1C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,uIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAU,OAAM;gCAAS,WAAU;;kDAC/C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,eAAe;gDAC9B,WAAU;0DAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEACX,gBAAgB,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,YAAY,SAAS;;;;;;kEAEjE,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;;;;;;;;;;;;;kDAM1D,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;;;;;;;;;;;;0DAIlB,6LAAC;gDAAI,WAAU;0DAA2F;;;;;;0DAK1G,6LAAC;gDAAI,WAAU;gDAAW,KAAK;;kEAC7B,6LAAC;wDACC,SAAS,IAAM,oBAAoB,CAAC;wDACpC,WAAU;;0EAEV,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EACb,MAAM,WAAW,OAAO,GAAG,iBAAiB,MAAM,MAAM,OAAO,GAAG;;;;;;;;;;;0EAGvE,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAW,CAAC,4CAA4C,EAAE,mBAAmB,eAAe,IAAI;;;;;;;;;;;;oDAI9G,kCACC,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAK,WAAU;0FACb,MAAM,WAAW,OAAO,GAAG,iBAAiB,MAAM,MAAM,OAAO,GAAG;;;;;;;;;;;sFAGvE,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAE,WAAU;8FACV,MAAM,aAAa,MAAM,WACtB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,MAAM,MAAM,IAAI,CAAC,EAAE;;;;;;8FAG/B,6LAAC;oFAAE,WAAU;;wFAAyB;wFAC/B,MAAM;;;;;;;8FAEb,6LAAC;oFAAE,WAAU;8FAAmC;;;;;;;;;;;;;;;;;;;;;;;0EAQtD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,+JAAA,CAAA,UAAI;wEACH,MAAK;wEACL,SAAS,IAAM,oBAAoB;wEACnC,WAAU;;0FAEV,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6LAAC;0FAAK;;;;;;;;;;;;kFAER,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEACC,SAAS;4EACP,oBAAoB;4EACpB;wEACF;wEACA,WAAU;;0FAEV,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYxB,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAzPa;;QAKc,2HAAA,CAAA,UAAO;;;KALrB", "debugId": null}}, {"offset": {"line": 2886, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/AdminDashboard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui';\nimport { Grid } from '@/components/layout';\nimport { \n  Users, \n  DollarSign, \n  TrendingUp, \n  Zap, \n  Shield, \n  CreditCard,\n  AlertTriangle,\n  CheckCircle\n} from 'lucide-react';\nimport { formatCurrency, formatNumber, formatTHS } from '@/lib/utils';\n\ninterface AdminStats {\n  totalUsers: number;\n  activeUsers: number;\n  pendingKYC: number;\n  approvedKYC: number;\n  totalInvestments: number;\n  totalEarningsDistributed: number;\n  totalTHSSold: number;\n  activeTHS: number;\n  pendingWithdrawals: number;\n  totalWithdrawals: number;\n  platformRevenue: number;\n  binaryPoolBalance: number;\n}\n\nexport const AdminDashboard: React.FC = () => {\n  const [stats, setStats] = useState<AdminStats | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchAdminStats();\n  }, []);\n\n  const fetchAdminStats = async () => {\n    try {\n      const response = await fetch('/api/admin/stats', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setStats(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch admin stats:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        {Array.from({ length: 4 }).map((_, i) => (\n          <div key={i} className=\"animate-pulse\">\n            <div className=\"h-32 bg-slate-700 rounded-xl\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  if (!stats) {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"text-center py-8\">\n          <p className=\"text-slate-400\">Failed to load admin statistics</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Section */}\n      <div className=\"bg-slate-800 border border-slate-700 rounded-xl p-6 text-white\">\n        <h1 className=\"text-2xl font-bold mb-2\">Admin Dashboard</h1>\n        <p className=\"text-slate-300\">\n          Monitor platform performance, manage users, and oversee all operations.\n        </p>\n      </div>\n\n      {/* User Statistics */}\n      <div>\n        <h2 className=\"text-lg font-semibold text-white mb-4\">User Management</h2>\n        <Grid cols={{ default: 1, md: 2, lg: 4 }} gap={6}>\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Total Users</p>\n                  <p className=\"text-2xl font-bold text-blue-400\">\n                    {formatNumber(stats.totalUsers, 0)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center\">\n                  <Users className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Active Users</p>\n                  <p className=\"text-2xl font-bold text-blue-400\">\n                    {formatNumber(stats.activeUsers, 0)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center\">\n                  <CheckCircle className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Pending KYC</p>\n                  <p className=\"text-2xl font-bold text-orange-400\">\n                    {formatNumber(stats.pendingKYC, 0)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center\">\n                  <Shield className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Approved KYC</p>\n                  <p className=\"text-2xl font-bold text-blue-400\">\n                    {formatNumber(stats.approvedKYC, 0)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center\">\n                  <Shield className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Financial Statistics */}\n      <div>\n        <h2 className=\"text-lg font-semibold text-white mb-4\">Financial Overview</h2>\n        <Grid cols={{ default: 1, md: 2, lg: 4 }} gap={6}>\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Total Investments</p>\n                  <p className=\"text-2xl font-bold text-blue-400\">\n                    {formatCurrency(stats.totalInvestments)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center\">\n                  <DollarSign className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Earnings Distributed</p>\n                  <p className=\"text-2xl font-bold text-blue-400\">\n                    {formatCurrency(stats.totalEarningsDistributed)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center\">\n                  <TrendingUp className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Platform Revenue</p>\n                  <p className=\"text-2xl font-bold text-orange-400\">\n                    {formatCurrency(stats.platformRevenue)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center\">\n                  <DollarSign className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Binary Pool</p>\n                  <p className=\"text-2xl font-bold text-red-400\">\n                    {formatCurrency(stats.binaryPoolBalance)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-red-600 rounded-full flex items-center justify-center\">\n                  <TrendingUp className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Mining Statistics */}\n      <div>\n        <h2 className=\"text-lg font-semibold text-white mb-4\">Mining Operations</h2>\n        <Grid cols={{ default: 1, md: 2 }} gap={6}>\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Total TH/s Sold</p>\n                  <p className=\"text-2xl font-bold text-orange-400\">\n                    {formatTHS(stats.totalTHSSold)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center\">\n                  <Zap className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Active TH/s</p>\n                  <p className=\"text-2xl font-bold text-blue-400\">\n                    {formatTHS(stats.activeTHS)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center\">\n                  <Zap className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Withdrawal Management */}\n      <div>\n        <h2 className=\"text-lg font-semibold text-white mb-4\">Withdrawal Management</h2>\n        <Grid cols={{ default: 1, md: 2 }} gap={6}>\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Pending Withdrawals</p>\n                  <p className=\"text-2xl font-bold text-red-400\">\n                    {formatNumber(stats.pendingWithdrawals, 0)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-red-600 rounded-full flex items-center justify-center\">\n                  <AlertTriangle className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-slate-400\">Total Withdrawals</p>\n                  <p className=\"text-2xl font-bold text-blue-400\">\n                    {formatCurrency(stats.totalWithdrawals)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center\">\n                  <CreditCard className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Quick Actions */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"text-white\">Quick Actions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <Grid cols={{ default: 1, md: 3 }} gap={4}>\n            <div className=\"p-4 border border-slate-600 rounded-lg hover:bg-slate-700 cursor-pointer transition-colors\">\n              <div className=\"flex items-center space-x-3\">\n                <Shield className=\"h-8 w-8 text-orange-400\" />\n                <div>\n                  <h3 className=\"font-medium text-white\">Review KYC</h3>\n                  <p className=\"text-sm text-slate-400\">{stats.pendingKYC} pending reviews</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"p-4 border border-slate-600 rounded-lg hover:bg-slate-700 cursor-pointer transition-colors\">\n              <div className=\"flex items-center space-x-3\">\n                <CreditCard className=\"h-8 w-8 text-red-400\" />\n                <div>\n                  <h3 className=\"font-medium text-white\">Process Withdrawals</h3>\n                  <p className=\"text-sm text-slate-400\">{stats.pendingWithdrawals} pending</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"p-4 border border-slate-600 rounded-lg hover:bg-slate-700 cursor-pointer transition-colors\">\n              <div className=\"flex items-center space-x-3\">\n                <Users className=\"h-8 w-8 text-blue-400\" />\n                <div>\n                  <h3 className=\"font-medium text-white\">Manage Users</h3>\n                  <p className=\"text-sm text-slate-400\">{stats.totalUsers} total users</p>\n                </div>\n              </div>\n            </div>\n          </Grid>\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AAfA;;;;;;AAgCO,MAAM,iBAA2B;;IACtC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,SAAS,KAAK,IAAI;gBACpB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;oBAAY,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;;;;;mBADP;;;;;;;;;;IAMlB;IAEA,IAAI,CAAC,OAAO;QACV,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAE,WAAU;8BAAiB;;;;;;;;;;;;;;;;IAItC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAAiB;;;;;;;;;;;;0BAMhC,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC,uIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CAC7C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,UAAU,EAAE;;;;;;;;;;;;0DAGpC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMzB,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,WAAW,EAAE;;;;;;;;;;;;0DAGrC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM/B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,UAAU,EAAE;;;;;;;;;;;;0DAGpC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM1B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,WAAW,EAAE;;;;;;;;;;;;0DAGrC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9B,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC,uIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CAC7C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,gBAAgB;;;;;;;;;;;;0DAG1C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,wBAAwB;;;;;;;;;;;;0DAGlD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,eAAe;;;;;;;;;;;;0DAGzC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,iBAAiB;;;;;;;;;;;;0DAG3C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlC,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC,uIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CACtC,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE,MAAM,YAAY;;;;;;;;;;;;0DAGjC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMvB,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE,MAAM,SAAS;;;;;;;;;;;;0DAG9B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3B,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC,uIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CACtC,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,kBAAkB,EAAE;;;;;;;;;;;;0DAG5C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMjC,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,gBAAgB;;;;;;;;;;;;0DAG1C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAa;;;;;;;;;;;kCAEpC,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,uIAAA,CAAA,OAAI;4BAAC,MAAM;gCAAE,SAAS;gCAAG,IAAI;4BAAE;4BAAG,KAAK;;8CACtC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAyB;;;;;;kEACvC,6LAAC;wDAAE,WAAU;;4DAA0B,MAAM,UAAU;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAK9D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAyB;;;;;;kEACvC,6LAAC;wDAAE,WAAU;;4DAA0B,MAAM,kBAAkB;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAKtE,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAyB;;;;;;kEACvC,6LAAC;wDAAE,WAAU;;4DAA0B,MAAM,UAAU;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1E;GA5Ta;KAAA", "debugId": null}}, {"offset": {"line": 4077, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/UserManagement.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardHeader, CardTitle, CardContent, Button, Input } from '@/components/ui';\nimport { Grid } from '@/components/layout';\nimport { \n  Users, \n  Search, \n  Filter, \n  MoreVertical, \n  Shield, \n  ShieldCheck, \n  ShieldX,\n  UserCheck,\n  UserX,\n  Eye,\n  Edit,\n  Trash2\n} from 'lucide-react';\nimport { formatDate } from '@/lib/utils';\n\ninterface User {\n  id: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  role: 'USER' | 'ADMIN';\n  kycStatus: 'PENDING' | 'APPROVED' | 'REJECTED';\n  isActive: boolean;\n  createdAt: string;\n  referralId: string;\n  totalInvestment?: number;\n  totalEarnings?: number;\n}\n\nexport const UserManagement: React.FC = () => {\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive' | 'pending_kyc'>('all');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n\n  useEffect(() => {\n    fetchUsers();\n  }, [currentPage, searchTerm, filterStatus]);\n\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        page: currentPage.toString(),\n        limit: '20',\n        search: searchTerm,\n        status: filterStatus,\n      });\n\n      const response = await fetch(`/api/admin/users?${params}`, {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setUsers(data.data.users);\n          setTotalPages(data.data.totalPages);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleUserAction = async (userId: string, action: 'activate' | 'deactivate' | 'promote' | 'demote') => {\n    try {\n      const response = await fetch('/api/admin/users/action', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({ userId, action }),\n      });\n\n      if (response.ok) {\n        fetchUsers(); // Refresh the list\n      }\n    } catch (error) {\n      console.error('Failed to perform user action:', error);\n    }\n  };\n\n  const getKYCStatusIcon = (status: string) => {\n    switch (status) {\n      case 'APPROVED':\n        return <ShieldCheck className=\"h-4 w-4 text-green-400\" />;\n      case 'REJECTED':\n        return <ShieldX className=\"h-4 w-4 text-red-400\" />;\n      default:\n        return <Shield className=\"h-4 w-4 text-yellow-400\" />;\n    }\n  };\n\n  const getStatusBadge = (isActive: boolean) => {\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n        isActive\n          ? 'bg-blue-600 text-white'\n          : 'bg-red-600 text-white'\n      }`}>\n        {isActive ? 'Active' : 'Inactive'}\n      </span>\n    );\n  };\n\n  const getRoleBadge = (role: string) => {\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n        role === 'ADMIN'\n          ? 'bg-red-600 text-white'\n          : 'bg-blue-600 text-white'\n      }`}>\n        {role}\n      </span>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-slate-700 rounded w-1/4 mb-4\"></div>\n          <div className=\"h-64 bg-slate-700 rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-white\">User Management</h1>\n          <p className=\"text-slate-400 mt-1\">Manage platform users and their permissions</p>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  placeholder=\"Search users by email or name...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400\"\n                />\n              </div>\n            </div>\n            <div className=\"sm:w-48\">\n              <select\n                value={filterStatus}\n                onChange={(e) => setFilterStatus(e.target.value as any)}\n                className=\"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"all\">All Users</option>\n                <option value=\"active\">Active</option>\n                <option value=\"inactive\">Inactive</option>\n                <option value=\"pending_kyc\">Pending KYC</option>\n              </select>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Users Table */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-white\">\n            <Users className=\"h-5 w-5\" />\n            Users ({users.length})\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead>\n                <tr className=\"border-b border-slate-600\">\n                  <th className=\"text-left py-3 px-4 font-medium text-white\">User</th>\n                  <th className=\"text-left py-3 px-4 font-medium text-white\">Role</th>\n                  <th className=\"text-left py-3 px-4 font-medium text-white\">KYC Status</th>\n                  <th className=\"text-left py-3 px-4 font-medium text-white\">Status</th>\n                  <th className=\"text-left py-3 px-4 font-medium text-white\">Joined</th>\n                  <th className=\"text-left py-3 px-4 font-medium text-white\">Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {users.map((user) => (\n                  <tr key={user.id} className=\"border-b border-slate-700 hover:bg-slate-700\">\n                    <td className=\"py-4 px-4\">\n                      <div>\n                        <div className=\"font-medium text-white\">\n                          {user.firstName} {user.lastName}\n                        </div>\n                        <div className=\"text-sm text-slate-400\">{user.email}</div>\n                        <div className=\"text-xs text-slate-500\">ID: {user.referralId}</div>\n                      </div>\n                    </td>\n                    <td className=\"py-4 px-4\">\n                      {getRoleBadge(user.role)}\n                    </td>\n                    <td className=\"py-4 px-4\">\n                      <div className=\"flex items-center gap-2\">\n                        {getKYCStatusIcon(user.kycStatus)}\n                        <span className=\"text-sm text-slate-300\">{user.kycStatus}</span>\n                      </div>\n                    </td>\n                    <td className=\"py-4 px-4\">\n                      {getStatusBadge(user.isActive)}\n                    </td>\n                    <td className=\"py-4 px-4 text-sm text-slate-400\">\n                      {formatDate(user.createdAt)}\n                    </td>\n                    <td className=\"py-4 px-4\">\n                      <div className=\"flex items-center gap-2\">\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => handleUserAction(user.id, user.isActive ? 'deactivate' : 'activate')}\n                          className=\"border-slate-600 text-slate-300 hover:bg-slate-700\"\n                        >\n                          {user.isActive ? <UserX className=\"h-4 w-4\" /> : <UserCheck className=\"h-4 w-4\" />}\n                        </Button>\n                        {user.role === 'USER' && (\n                          <Button\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={() => handleUserAction(user.id, 'promote')}\n                            title=\"Promote to Admin\"\n                            className=\"border-slate-600 text-slate-300 hover:bg-slate-700\"\n                          >\n                            <Shield className=\"h-4 w-4\" />\n                          </Button>\n                        )}\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n\n          {/* Pagination */}\n          {totalPages > 1 && (\n            <div className=\"flex items-center justify-between mt-6\">\n              <div className=\"text-sm text-slate-400\">\n                Page {currentPage} of {totalPages}\n              </div>\n              <div className=\"flex gap-2\">\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}\n                  disabled={currentPage === 1}\n                  className=\"border-slate-600 text-slate-300 hover:bg-slate-700\"\n                >\n                  Previous\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}\n                  disabled={currentPage === totalPages}\n                  className=\"border-slate-600 text-slate-300 hover:bg-slate-700\"\n                >\n                  Next\n                </Button>\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;AAnBA;;;;;AAmCO,MAAM,iBAA2B;;IACtC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiD;IAChG,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG;QAAC;QAAa;QAAY;KAAa;IAE1C,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,YAAY,QAAQ;gBAC1B,OAAO;gBACP,QAAQ;gBACR,QAAQ;YACV;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,QAAQ,EAAE;gBACzD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,SAAS,KAAK,IAAI,CAAC,KAAK;oBACxB,cAAc,KAAK,IAAI,CAAC,UAAU;gBACpC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO,QAAgB;QAC9C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAQ;gBAAO;YACxC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,cAAc,mBAAmB;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QAC7B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,qBACE,6LAAC;YAAK,WAAW,CAAC,wEAAwE,EACxF,WACI,2BACA,yBACJ;sBACC,WAAW,WAAW;;;;;;IAG7B;IAEA,MAAM,eAAe,CAAC;QACpB,qBACE,6LAAC;YAAK,WAAW,CAAC,wEAAwE,EACxF,SAAS,UACL,0BACA,0BACJ;sBACC;;;;;;IAGP;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAC9C,6LAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;;;;;;0BAKvC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;;;;;;0CAIhB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,6LAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,6LAAC;4CAAO,OAAM;sDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;gCACrB,MAAM,MAAM;gCAAC;;;;;;;;;;;;kCAGzB,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;sDACC,cAAA,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;;;;;;;;;;;;sDAG/D,6LAAC;sDACE,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;oDAAiB,WAAU;;sEAC1B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;;4EACZ,KAAK,SAAS;4EAAC;4EAAE,KAAK,QAAQ;;;;;;;kFAEjC,6LAAC;wEAAI,WAAU;kFAA0B,KAAK,KAAK;;;;;;kFACnD,6LAAC;wEAAI,WAAU;;4EAAyB;4EAAK,KAAK,UAAU;;;;;;;;;;;;;;;;;;sEAGhE,6LAAC;4DAAG,WAAU;sEACX,aAAa,KAAK,IAAI;;;;;;sEAEzB,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;oEACZ,iBAAiB,KAAK,SAAS;kFAChC,6LAAC;wEAAK,WAAU;kFAA0B,KAAK,SAAS;;;;;;;;;;;;;;;;;sEAG5D,6LAAC;4DAAG,WAAU;sEACX,eAAe,KAAK,QAAQ;;;;;;sEAE/B,6LAAC;4DAAG,WAAU;sEACX,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS;;;;;;sEAE5B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,QAAQ,GAAG,eAAe;wEACxE,WAAU;kFAET,KAAK,QAAQ,iBAAG,6LAAC,2MAAA,CAAA,QAAK;4EAAC,WAAU;;;;;iGAAe,6LAAC,mNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;;;;;;oEAEvE,KAAK,IAAI,KAAK,wBACb,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE;wEACzC,OAAM;wEACN,WAAU;kFAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mDA3CnB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;4BAuDvB,aAAa,mBACZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAAyB;4CAChC;4CAAY;4CAAK;;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;gDACzD,UAAU,gBAAgB;gDAC1B,WAAU;0DACX;;;;;;0DAGD,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,YAAY,OAAO;gDAClE,UAAU,gBAAgB;gDAC1B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GA/Pa;KAAA", "debugId": null}}, {"offset": {"line": 4746, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/KYCReview.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, <PERSON>H<PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent, Button, Input } from '@/components/ui';\nimport { \n  Shield, \n  Eye, \n  Check, \n  X, \n  Download, \n  FileText,\n  User,\n  Calendar,\n  AlertTriangle\n} from 'lucide-react';\nimport { formatDate } from '@/lib/utils';\n\ninterface KYCDocument {\n  id: string;\n  userId: string;\n  user: {\n    firstName: string;\n    lastName: string;\n    email: string;\n    referralId: string;\n  };\n  documentType: string;\n  documentUrl: string;\n  status: 'PENDING' | 'APPROVED' | 'REJECTED';\n  submittedAt: string;\n  reviewedAt?: string;\n  rejectionReason?: string;\n}\n\nexport const KYCReview: React.FC = () => {\n  const [documents, setDocuments] = useState<KYCDocument[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedDocument, setSelectedDocument] = useState<KYCDocument | null>(null);\n  const [reviewAction, setReviewAction] = useState<'approve' | 'reject' | null>(null);\n  const [rejectionReason, setRejectionReason] = useState('');\n  const [processing, setProcessing] = useState(false);\n\n  useEffect(() => {\n    fetchPendingKYC();\n  }, []);\n\n  const fetchPendingKYC = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/admin/kyc/pending', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setDocuments(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch KYC documents:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReview = async (documentId: string, action: 'approve' | 'reject', reason?: string) => {\n    try {\n      setProcessing(true);\n      const response = await fetch('/api/admin/kyc/review', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({\n          documentId,\n          action: action.toUpperCase(),\n          rejectionReason: reason,\n        }),\n      });\n\n      if (response.ok) {\n        fetchPendingKYC(); // Refresh the list\n        setSelectedDocument(null);\n        setReviewAction(null);\n        setRejectionReason('');\n      }\n    } catch (error) {\n      console.error('Failed to review KYC document:', error);\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    const colors = {\n      PENDING: 'bg-yellow-900 text-yellow-300 border border-yellow-700',\n      APPROVED: 'bg-green-900 text-green-300 border border-green-700',\n      REJECTED: 'bg-red-900 text-red-300 border border-red-700',\n    };\n\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[status as keyof typeof colors]}`}>\n        {status}\n      </span>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-slate-700 rounded w-1/4 mb-4\"></div>\n          <div className=\"h-64 bg-slate-700 rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-white\">KYC Review</h1>\n          <p className=\"text-slate-400 mt-1\">Review and approve user KYC documents</p>\n        </div>\n        <div className=\"flex items-center gap-2 text-sm text-slate-400\">\n          <AlertTriangle className=\"h-4 w-4 text-orange-400\" />\n          {documents.length} pending reviews\n        </div>\n      </div>\n\n      {/* KYC Documents */}\n      <div className=\"grid gap-6\">\n        {documents.length === 0 ? (\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-12 text-center\">\n              <Shield className=\"h-12 w-12 text-slate-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-white mb-2\">No Pending KYC Reviews</h3>\n              <p className=\"text-slate-400\">All KYC documents have been reviewed.</p>\n            </CardContent>\n          </Card>\n        ) : (\n          documents.map((doc) => (\n            <Card key={doc.id} className=\"bg-slate-800 border-slate-700 hover:bg-slate-750 transition-colors\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center gap-3 mb-3\">\n                      <div className=\"flex items-center gap-2\">\n                        <User className=\"h-5 w-5 text-slate-400\" />\n                        <span className=\"font-medium text-white\">\n                          {doc.user.firstName} {doc.user.lastName}\n                        </span>\n                      </div>\n                      {getStatusBadge(doc.status)}\n                    </div>\n\n                    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-slate-400 mb-4\">\n                      <div>\n                        <span className=\"font-medium text-slate-300\">Email:</span> {doc.user.email}\n                      </div>\n                      <div>\n                        <span className=\"font-medium text-slate-300\">User ID:</span> {doc.user.referralId}\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Calendar className=\"h-4 w-4\" />\n                        <span className=\"font-medium text-slate-300\">Submitted:</span> {formatDate(doc.submittedAt)}\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center gap-2 text-sm text-slate-400 mb-4\">\n                      <FileText className=\"h-4 w-4\" />\n                      <span className=\"font-medium text-slate-300\">Document Type:</span> {doc.documentType}\n                    </div>\n\n                    {doc.rejectionReason && (\n                      <div className=\"bg-red-900/20 border border-red-700 rounded-lg p-3 mb-4\">\n                        <div className=\"flex items-center gap-2 text-red-300 text-sm font-medium mb-1\">\n                          <X className=\"h-4 w-4\" />\n                          Rejection Reason\n                        </div>\n                        <p className=\"text-red-400 text-sm\">{doc.rejectionReason}</p>\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"flex items-center gap-2 ml-4\">\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => window.open(doc.documentUrl, '_blank')}\n                      className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white\"\n                    >\n                      <Eye className=\"h-4 w-4 mr-1\" />\n                      View\n                    </Button>\n\n                    {doc.status === 'PENDING' && (\n                      <>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => {\n                            setSelectedDocument(doc);\n                            setReviewAction('approve');\n                          }}\n                          className=\"border-green-600 text-green-400 hover:text-green-300 hover:bg-green-900/20\"\n                        >\n                          <Check className=\"h-4 w-4 mr-1\" />\n                          Approve\n                        </Button>\n\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => {\n                            setSelectedDocument(doc);\n                            setReviewAction('reject');\n                          }}\n                          className=\"border-red-600 text-red-400 hover:text-red-300 hover:bg-red-900/20\"\n                        >\n                          <X className=\"h-4 w-4 mr-1\" />\n                          Reject\n                        </Button>\n                      </>\n                    )}\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))\n        )}\n      </div>\n\n      {/* Review Modal */}\n      {selectedDocument && reviewAction && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-slate-800 border border-slate-700 rounded-xl max-w-md w-full p-6\">\n            <h3 className=\"text-lg font-semibold mb-4 text-white\">\n              {reviewAction === 'approve' ? 'Approve KYC Document' : 'Reject KYC Document'}\n            </h3>\n\n            <div className=\"mb-4\">\n              <p className=\"text-slate-400 mb-2\">\n                User: <span className=\"font-medium text-white\">{selectedDocument.user.firstName} {selectedDocument.user.lastName}</span>\n              </p>\n              <p className=\"text-slate-400\">\n                Document: <span className=\"font-medium text-white\">{selectedDocument.documentType}</span>\n              </p>\n            </div>\n\n            {reviewAction === 'reject' && (\n              <div className=\"mb-4\">\n                <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                  Rejection Reason *\n                </label>\n                <textarea\n                  value={rejectionReason}\n                  onChange={(e) => setRejectionReason(e.target.value)}\n                  className=\"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white placeholder-slate-400 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                  rows={3}\n                  placeholder=\"Please provide a reason for rejection...\"\n                  required\n                />\n              </div>\n            )}\n\n            <div className=\"flex gap-3\">\n              <Button\n                variant=\"outline\"\n                onClick={() => {\n                  setSelectedDocument(null);\n                  setReviewAction(null);\n                  setRejectionReason('');\n                }}\n                disabled={processing}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white\"\n              >\n                Cancel\n              </Button>\n              <Button\n                onClick={() => handleReview(\n                  selectedDocument.id,\n                  reviewAction,\n                  reviewAction === 'reject' ? rejectionReason : undefined\n                )}\n                disabled={processing || (reviewAction === 'reject' && !rejectionReason.trim())}\n                loading={processing}\n                className={reviewAction === 'approve'\n                  ? 'bg-green-600 hover:bg-green-700 text-white'\n                  : 'bg-red-600 hover:bg-red-700 text-white'\n                }\n              >\n                {reviewAction === 'approve' ? 'Approve' : 'Reject'}\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAfA;;;;;AAkCO,MAAM,YAAsB;;IACjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC7E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;IAC9E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,aAAa,KAAK,IAAI;gBACxB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO,YAAoB,QAA8B;QAC5E,IAAI;YACF,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,QAAQ,OAAO,WAAW;oBAC1B,iBAAiB;gBACnB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,mBAAmB,mBAAmB;gBACtC,oBAAoB;gBACpB,gBAAgB;gBAChB,mBAAmB;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,SAAS;YACT,UAAU;YACV,UAAU;QACZ;QAEA,qBACE,6LAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,MAAM,CAAC,OAA8B,EAAE;sBAChI;;;;;;IAGP;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAgC;;;;;;0CAC9C,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;kCAErC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;4BACxB,UAAU,MAAM;4BAAC;;;;;;;;;;;;;0BAKtB,6LAAC;gBAAI,WAAU;0BACZ,UAAU,MAAM,KAAK,kBACpB,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAE,WAAU;0CAAiB;;;;;;;;;;;;;;;;2BAIlC,UAAU,GAAG,CAAC,CAAC,oBACb,6LAAC,mIAAA,CAAA,OAAI;wBAAc,WAAU;kCAC3B,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;gEAAK,WAAU;;oEACb,IAAI,IAAI,CAAC,SAAS;oEAAC;oEAAE,IAAI,IAAI,CAAC,QAAQ;;;;;;;;;;;;;oDAG1C,eAAe,IAAI,MAAM;;;;;;;0DAG5B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAA6B;;;;;;4DAAa;4DAAE,IAAI,IAAI,CAAC,KAAK;;;;;;;kEAE5E,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAA6B;;;;;;4DAAe;4DAAE,IAAI,IAAI,CAAC,UAAU;;;;;;;kEAEnF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;gEAAK,WAAU;0EAA6B;;;;;;4DAAiB;4DAAE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,IAAI,WAAW;;;;;;;;;;;;;0DAI9F,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;oDAAqB;oDAAE,IAAI,YAAY;;;;;;;4CAGrF,IAAI,eAAe,kBAClB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAG3B,6LAAC;wDAAE,WAAU;kEAAwB,IAAI,eAAe;;;;;;;;;;;;;;;;;;kDAK9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC,IAAI,WAAW,EAAE;gDAC5C,WAAU;;kEAEV,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAIjC,IAAI,MAAM,KAAK,2BACd;;kEACE,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,oBAAoB;4DACpB,gBAAgB;wDAClB;wDACA,WAAU;;0EAEV,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAIpC,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,oBAAoB;4DACpB,gBAAgB;wDAClB;wDACA,WAAU;;0EAEV,6LAAC,+LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;uBA9EjC,IAAI,EAAE;;;;;;;;;;YA4FtB,oBAAoB,8BACnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,iBAAiB,YAAY,yBAAyB;;;;;;sCAGzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;wCAAsB;sDAC3B,6LAAC;4CAAK,WAAU;;gDAA0B,iBAAiB,IAAI,CAAC,SAAS;gDAAC;gDAAE,iBAAiB,IAAI,CAAC,QAAQ;;;;;;;;;;;;;8CAElH,6LAAC;oCAAE,WAAU;;wCAAiB;sDAClB,6LAAC;4CAAK,WAAU;sDAA0B,iBAAiB,YAAY;;;;;;;;;;;;;;;;;;wBAIpF,iBAAiB,0BAChB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAgD;;;;;;8CAGjE,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,WAAU;oCACV,MAAM;oCACN,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;wCACP,oBAAoB;wCACpB,gBAAgB;wCAChB,mBAAmB;oCACrB;oCACA,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,aACb,iBAAiB,EAAE,EACnB,cACA,iBAAiB,WAAW,kBAAkB;oCAEhD,UAAU,cAAe,iBAAiB,YAAY,CAAC,gBAAgB,IAAI;oCAC3E,SAAS;oCACT,WAAW,iBAAiB,YACxB,+CACA;8CAGH,iBAAiB,YAAY,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1D;GA9Qa;KAAA", "debugId": null}}, {"offset": {"line": 5411, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/WithdrawalManagement.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, Card<PERSON>eader, Card<PERSON><PERSON>le, CardContent, Button, Input } from '@/components/ui';\nimport { \n  CreditCard, \n  Search, \n  Filter, \n  Check, \n  X, \n  Clock,\n  DollarSign,\n  User,\n  Calendar,\n  AlertTriangle,\n  CheckCircle,\n  XCircle\n} from 'lucide-react';\nimport { formatCurrency, formatDate } from '@/lib/utils';\n\ninterface WithdrawalRequest {\n  id: string;\n  userId: string;\n  user: {\n    firstName: string;\n    lastName: string;\n    email: string;\n    referralId: string;\n  };\n  amount: number;\n  walletAddress: string;\n  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'COMPLETED';\n  requestedAt: string;\n  processedAt?: string;\n  rejectionReason?: string;\n  transactionHash?: string;\n}\n\nexport const WithdrawalManagement: React.FC = () => {\n  const [withdrawals, setWithdrawals] = useState<WithdrawalRequest[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'approved' | 'rejected' | 'completed'>('all');\n  const [selectedWithdrawal, setSelectedWithdrawal] = useState<WithdrawalRequest | null>(null);\n  const [reviewAction, setReviewAction] = useState<'approve' | 'reject' | 'complete' | null>(null);\n  const [rejectionReason, setRejectionReason] = useState('');\n  const [transactionHash, setTransactionHash] = useState('');\n  const [processing, setProcessing] = useState(false);\n\n  useEffect(() => {\n    fetchWithdrawals();\n  }, [searchTerm, filterStatus]);\n\n  const fetchWithdrawals = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        search: searchTerm,\n        status: filterStatus,\n      });\n\n      const response = await fetch(`/api/admin/withdrawals?${params}`, {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setWithdrawals(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch withdrawals:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleWithdrawalAction = async (\n    withdrawalId: string, \n    action: 'approve' | 'reject' | 'complete', \n    data?: { rejectionReason?: string; transactionHash?: string }\n  ) => {\n    try {\n      setProcessing(true);\n      const response = await fetch('/api/admin/withdrawals/action', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({\n          withdrawalId,\n          action: action.toUpperCase(),\n          ...data,\n        }),\n      });\n\n      if (response.ok) {\n        fetchWithdrawals(); // Refresh the list\n        setSelectedWithdrawal(null);\n        setReviewAction(null);\n        setRejectionReason('');\n        setTransactionHash('');\n      }\n    } catch (error) {\n      console.error('Failed to process withdrawal action:', error);\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    const configs = {\n      PENDING: { color: 'bg-yellow-900 text-yellow-300 border border-yellow-700', icon: Clock },\n      APPROVED: { color: 'bg-blue-900 text-blue-300 border border-blue-700', icon: CheckCircle },\n      REJECTED: { color: 'bg-red-900 text-red-300 border border-red-700', icon: XCircle },\n      COMPLETED: { color: 'bg-green-900 text-green-300 border border-green-700', icon: CheckCircle },\n    };\n\n    const config = configs[status as keyof typeof configs];\n    const Icon = config.icon;\n\n    return (\n      <span className={`inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>\n        <Icon className=\"h-3 w-3\" />\n        {status}\n      </span>\n    );\n  };\n\n  const getTotalPendingAmount = () => {\n    return withdrawals\n      .filter(w => w.status === 'PENDING')\n      .reduce((sum, w) => sum + w.amount, 0);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-slate-700 rounded w-1/4 mb-4\"></div>\n          <div className=\"h-64 bg-slate-700 rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-white\">Withdrawal Management</h1>\n          <p className=\"text-slate-400 mt-1\">Review and process user withdrawal requests</p>\n        </div>\n        <div className=\"text-right\">\n          <div className=\"text-sm text-slate-400\">Pending Amount</div>\n          <div className=\"text-2xl font-bold text-yellow-400\">\n            {formatCurrency(getTotalPendingAmount())}\n          </div>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  placeholder=\"Search by user email or wallet address...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500\"\n                />\n              </div>\n            </div>\n            <div className=\"sm:w-48\">\n              <select\n                value={filterStatus}\n                onChange={(e) => setFilterStatus(e.target.value as any)}\n                className=\"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n              >\n                <option value=\"all\">All Withdrawals</option>\n                <option value=\"pending\">Pending</option>\n                <option value=\"approved\">Approved</option>\n                <option value=\"completed\">Completed</option>\n                <option value=\"rejected\">Rejected</option>\n              </select>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Withdrawals List */}\n      <div className=\"grid gap-4\">\n        {withdrawals.length === 0 ? (\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-12 text-center\">\n              <CreditCard className=\"h-12 w-12 text-slate-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-white mb-2\">No Withdrawal Requests</h3>\n              <p className=\"text-slate-400\">No withdrawal requests match your current filters.</p>\n            </CardContent>\n          </Card>\n        ) : (\n          withdrawals.map((withdrawal) => (\n            <Card key={withdrawal.id} className=\"bg-slate-800 border-slate-700 hover:bg-slate-750 transition-colors\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center gap-3 mb-3\">\n                      <div className=\"flex items-center gap-2\">\n                        <User className=\"h-5 w-5 text-slate-400\" />\n                        <span className=\"font-medium text-white\">\n                          {withdrawal.user.firstName} {withdrawal.user.lastName}\n                        </span>\n                      </div>\n                      {getStatusBadge(withdrawal.status)}\n                    </div>\n\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-slate-400 mb-4\">\n                      <div>\n                        <span className=\"font-medium text-slate-300\">Email:</span> {withdrawal.user.email}\n                      </div>\n                      <div>\n                        <span className=\"font-medium text-slate-300\">User ID:</span> {withdrawal.user.referralId}\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <DollarSign className=\"h-4 w-4\" />\n                        <span className=\"font-medium text-slate-300\">Amount:</span> {formatCurrency(withdrawal.amount)}\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Calendar className=\"h-4 w-4\" />\n                        <span className=\"font-medium text-slate-300\">Requested:</span> {formatDate(withdrawal.requestedAt)}\n                      </div>\n                    </div>\n\n                    <div className=\"text-sm text-slate-400 mb-4\">\n                      <span className=\"font-medium text-slate-300\">Wallet Address:</span>\n                      <div className=\"font-mono text-xs bg-slate-700 border border-slate-600 p-2 rounded mt-1 break-all text-slate-300\">\n                        {withdrawal.walletAddress}\n                      </div>\n                    </div>\n\n                    {withdrawal.transactionHash && (\n                      <div className=\"text-sm text-slate-400 mb-4\">\n                        <span className=\"font-medium text-slate-300\">Transaction Hash:</span>\n                        <div className=\"font-mono text-xs bg-green-900/20 border border-green-700 p-2 rounded mt-1 break-all text-green-300\">\n                          {withdrawal.transactionHash}\n                        </div>\n                      </div>\n                    )}\n\n                    {withdrawal.rejectionReason && (\n                      <div className=\"bg-red-900/20 border border-red-700 rounded-lg p-3 mb-4\">\n                        <div className=\"flex items-center gap-2 text-red-300 text-sm font-medium mb-1\">\n                          <X className=\"h-4 w-4\" />\n                          Rejection Reason\n                        </div>\n                        <p className=\"text-red-400 text-sm\">{withdrawal.rejectionReason}</p>\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"flex items-center gap-2 ml-4\">\n                    {withdrawal.status === 'PENDING' && (\n                      <>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => {\n                            setSelectedWithdrawal(withdrawal);\n                            setReviewAction('approve');\n                          }}\n                          className=\"border-green-600 text-green-400 hover:text-green-300 hover:bg-green-900/20\"\n                        >\n                          <Check className=\"h-4 w-4 mr-1\" />\n                          Approve\n                        </Button>\n\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => {\n                            setSelectedWithdrawal(withdrawal);\n                            setReviewAction('reject');\n                          }}\n                          className=\"border-red-600 text-red-400 hover:text-red-300 hover:bg-red-900/20\"\n                        >\n                          <X className=\"h-4 w-4 mr-1\" />\n                          Reject\n                        </Button>\n                      </>\n                    )}\n\n                    {withdrawal.status === 'APPROVED' && (\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={() => {\n                          setSelectedWithdrawal(withdrawal);\n                          setReviewAction('complete');\n                        }}\n                        className=\"border-blue-600 text-blue-400 hover:text-blue-300 hover:bg-blue-900/20\"\n                      >\n                        <CheckCircle className=\"h-4 w-4 mr-1\" />\n                        Mark Complete\n                      </Button>\n                    )}\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))\n        )}\n      </div>\n\n      {/* Action Modal */}\n      {selectedWithdrawal && reviewAction && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-slate-800 border border-slate-700 rounded-xl max-w-md w-full p-6\">\n            <h3 className=\"text-lg font-semibold mb-4 text-white\">\n              {reviewAction === 'approve' && 'Approve Withdrawal'}\n              {reviewAction === 'reject' && 'Reject Withdrawal'}\n              {reviewAction === 'complete' && 'Complete Withdrawal'}\n            </h3>\n\n            <div className=\"mb-4\">\n              <p className=\"text-slate-400 mb-2\">\n                User: <span className=\"font-medium text-white\">{selectedWithdrawal.user.firstName} {selectedWithdrawal.user.lastName}</span>\n              </p>\n              <p className=\"text-slate-400\">\n                Amount: <span className=\"font-medium text-white\">{formatCurrency(selectedWithdrawal.amount)}</span>\n              </p>\n            </div>\n\n            {reviewAction === 'reject' && (\n              <div className=\"mb-4\">\n                <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                  Rejection Reason *\n                </label>\n                <textarea\n                  value={rejectionReason}\n                  onChange={(e) => setRejectionReason(e.target.value)}\n                  className=\"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white placeholder-slate-400 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                  rows={3}\n                  placeholder=\"Please provide a reason for rejection...\"\n                  required\n                />\n              </div>\n            )}\n\n            {reviewAction === 'complete' && (\n              <div className=\"mb-4\">\n                <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                  Transaction Hash *\n                </label>\n                <Input\n                  value={transactionHash}\n                  onChange={(e) => setTransactionHash(e.target.value)}\n                  placeholder=\"Enter blockchain transaction hash...\"\n                  className=\"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500\"\n                  required\n                />\n              </div>\n            )}\n\n            <div className=\"flex gap-3\">\n              <Button\n                variant=\"outline\"\n                onClick={() => {\n                  setSelectedWithdrawal(null);\n                  setReviewAction(null);\n                  setRejectionReason('');\n                  setTransactionHash('');\n                }}\n                disabled={processing}\n                className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white\"\n              >\n                Cancel\n              </Button>\n              <Button\n                onClick={() => {\n                  const data: any = {};\n                  if (reviewAction === 'reject') data.rejectionReason = rejectionReason;\n                  if (reviewAction === 'complete') data.transactionHash = transactionHash;\n\n                  handleWithdrawalAction(selectedWithdrawal.id, reviewAction, data);\n                }}\n                disabled={\n                  processing ||\n                  (reviewAction === 'reject' && !rejectionReason.trim()) ||\n                  (reviewAction === 'complete' && !transactionHash.trim())\n                }\n                loading={processing}\n                className={\n                  reviewAction === 'reject'\n                    ? 'bg-red-600 hover:bg-red-700 text-white'\n                    : reviewAction === 'approve'\n                    ? 'bg-green-600 hover:bg-green-700 text-white'\n                    : 'bg-blue-600 hover:bg-blue-700 text-white'\n                }\n              >\n                {reviewAction === 'approve' && 'Approve'}\n                {reviewAction === 'reject' && 'Reject'}\n                {reviewAction === 'complete' && 'Mark Complete'}\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;AAlBA;;;;;AAsCO,MAAM,uBAAiC;;IAC5C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6D;IAC5G,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IACvF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4C;IAC3F,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR;QACF;yCAAG;QAAC;QAAY;KAAa;IAE7B,MAAM,mBAAmB;QACvB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC,QAAQ;gBACR,QAAQ;YACV;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,QAAQ,EAAE;gBAC/D,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,eAAe,KAAK,IAAI;gBAC1B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB,OAC7B,cACA,QACA;QAEA,IAAI;YACF,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,iCAAiC;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,QAAQ,OAAO,WAAW;oBAC1B,GAAG,IAAI;gBACT;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,oBAAoB,mBAAmB;gBACvC,sBAAsB;gBACtB,gBAAgB;gBAChB,mBAAmB;gBACnB,mBAAmB;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU;YACd,SAAS;gBAAE,OAAO;gBAA0D,MAAM,uMAAA,CAAA,QAAK;YAAC;YACxF,UAAU;gBAAE,OAAO;gBAAoD,MAAM,8NAAA,CAAA,cAAW;YAAC;YACzF,UAAU;gBAAE,OAAO;gBAAiD,MAAM,+MAAA,CAAA,UAAO;YAAC;YAClF,WAAW;gBAAE,OAAO;gBAAuD,MAAM,8NAAA,CAAA,cAAW;YAAC;QAC/F;QAEA,MAAM,SAAS,OAAO,CAAC,OAA+B;QACtD,MAAM,OAAO,OAAO,IAAI;QAExB,qBACE,6LAAC;YAAK,WAAW,CAAC,8EAA8E,EAAE,OAAO,KAAK,EAAE;;8BAC9G,6LAAC;oBAAK,WAAU;;;;;;gBACf;;;;;;;IAGP;IAEA,MAAM,wBAAwB;QAC5B,OAAO,YACJ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WACzB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IACxC;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAgC;;;;;;0CAC9C,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;kCAErC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAyB;;;;;;0CACxC,6LAAC;gCAAI,WAAU;0CACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;;0BAMtB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;;;;;;0CAIhB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,6LAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,6LAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,6LAAC;4CAAO,OAAM;sDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnC,6LAAC;gBAAI,WAAU;0BACZ,YAAY,MAAM,KAAK,kBACtB,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAE,WAAU;0CAAiB;;;;;;;;;;;;;;;;2BAIlC,YAAY,GAAG,CAAC,CAAC,2BACf,6LAAC,mIAAA,CAAA,OAAI;wBAAqB,WAAU;kCAClC,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;gEAAK,WAAU;;oEACb,WAAW,IAAI,CAAC,SAAS;oEAAC;oEAAE,WAAW,IAAI,CAAC,QAAQ;;;;;;;;;;;;;oDAGxD,eAAe,WAAW,MAAM;;;;;;;0DAGnC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAA6B;;;;;;4DAAa;4DAAE,WAAW,IAAI,CAAC,KAAK;;;;;;;kEAEnF,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAA6B;;;;;;4DAAe;4DAAE,WAAW,IAAI,CAAC,UAAU;;;;;;;kEAE1F,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC;gEAAK,WAAU;0EAA6B;;;;;;4DAAc;4DAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,MAAM;;;;;;;kEAE/F,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;gEAAK,WAAU;0EAA6B;;;;;;4DAAiB;4DAAE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,WAAW;;;;;;;;;;;;;0DAIrG,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;kEAC7C,6LAAC;wDAAI,WAAU;kEACZ,WAAW,aAAa;;;;;;;;;;;;4CAI5B,WAAW,eAAe,kBACzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;kEAC7C,6LAAC;wDAAI,WAAU;kEACZ,WAAW,eAAe;;;;;;;;;;;;4CAKhC,WAAW,eAAe,kBACzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAG3B,6LAAC;wDAAE,WAAU;kEAAwB,WAAW,eAAe;;;;;;;;;;;;;;;;;;kDAKrE,6LAAC;wCAAI,WAAU;;4CACZ,WAAW,MAAM,KAAK,2BACrB;;kEACE,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,sBAAsB;4DACtB,gBAAgB;wDAClB;wDACA,WAAU;;0EAEV,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAIpC,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,sBAAsB;4DACtB,gBAAgB;wDAClB;wDACA,WAAU;;0EAEV,6LAAC,+LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;4CAMnC,WAAW,MAAM,KAAK,4BACrB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;oDACP,sBAAsB;oDACtB,gBAAgB;gDAClB;gDACA,WAAU;;kEAEV,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;uBAnGzC,WAAW,EAAE;;;;;;;;;;YAgH7B,sBAAsB,8BACrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCACX,iBAAiB,aAAa;gCAC9B,iBAAiB,YAAY;gCAC7B,iBAAiB,cAAc;;;;;;;sCAGlC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;wCAAsB;sDAC3B,6LAAC;4CAAK,WAAU;;gDAA0B,mBAAmB,IAAI,CAAC,SAAS;gDAAC;gDAAE,mBAAmB,IAAI,CAAC,QAAQ;;;;;;;;;;;;;8CAEtH,6LAAC;oCAAE,WAAU;;wCAAiB;sDACpB,6LAAC;4CAAK,WAAU;sDAA0B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,mBAAmB,MAAM;;;;;;;;;;;;;;;;;;wBAI7F,iBAAiB,0BAChB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAgD;;;;;;8CAGjE,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,WAAU;oCACV,MAAM;oCACN,aAAY;oCACZ,QAAQ;;;;;;;;;;;;wBAKb,iBAAiB,4BAChB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAgD;;;;;;8CAGjE,6LAAC,oIAAA,CAAA,QAAK;oCACJ,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,aAAY;oCACZ,WAAU;oCACV,QAAQ;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;wCACP,sBAAsB;wCACtB,gBAAgB;wCAChB,mBAAmB;wCACnB,mBAAmB;oCACrB;oCACA,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;wCACP,MAAM,OAAY,CAAC;wCACnB,IAAI,iBAAiB,UAAU,KAAK,eAAe,GAAG;wCACtD,IAAI,iBAAiB,YAAY,KAAK,eAAe,GAAG;wCAExD,uBAAuB,mBAAmB,EAAE,EAAE,cAAc;oCAC9D;oCACA,UACE,cACC,iBAAiB,YAAY,CAAC,gBAAgB,IAAI,MAClD,iBAAiB,cAAc,CAAC,gBAAgB,IAAI;oCAEvD,SAAS;oCACT,WACE,iBAAiB,WACb,2CACA,iBAAiB,YACjB,+CACA;;wCAGL,iBAAiB,aAAa;wCAC9B,iBAAiB,YAAY;wCAC7B,iBAAiB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;GAzXa;KAAA", "debugId": null}}, {"offset": {"line": 6333, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/SystemSettings.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardHeader, CardTitle, CardContent, Button, Input } from '@/components/ui';\nimport { \n  Settings, \n  Save, \n  DollarSign, \n  Percent, \n  Zap,\n  Users,\n  Shield,\n  AlertTriangle,\n  CheckCircle\n} from 'lucide-react';\nimport { formatCurrency } from '@/lib/utils';\n\ninterface SystemSettings {\n  // Mining Unit Pricing\n  thsPriceUSD: number;\n  minPurchaseAmount: number;\n  maxPurchaseAmount: number;\n  \n  // Earnings Configuration\n  dailyROIPercentage: number;\n  binaryBonusPercentage: number;\n  referralBonusPercentage: number;\n  \n  // Withdrawal Settings\n  minWithdrawalAmount: number;\n  withdrawalFeePercentage: number;\n  withdrawalProcessingDays: number;\n  \n  // Platform Settings\n  platformFeePercentage: number;\n  maintenanceMode: boolean;\n  registrationEnabled: boolean;\n  kycRequired: boolean;\n}\n\nexport const SystemSettings: React.FC = () => {\n  const [settings, setSettings] = useState<SystemSettings | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [saveSuccess, setSaveSuccess] = useState(false);\n\n  useEffect(() => {\n    fetchSettings();\n  }, []);\n\n  const fetchSettings = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/admin/settings', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setSettings(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch settings:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSave = async () => {\n    if (!settings) return;\n\n    try {\n      setSaving(true);\n      const response = await fetch('/api/admin/settings', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify(settings),\n      });\n\n      if (response.ok) {\n        setSaveSuccess(true);\n        setTimeout(() => setSaveSuccess(false), 3000);\n      }\n    } catch (error) {\n      console.error('Failed to save settings:', error);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const updateSetting = (key: keyof SystemSettings, value: any) => {\n    if (!settings) return;\n    setSettings({ ...settings, [key]: value });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-slate-700 rounded w-1/4 mb-4\"></div>\n          <div className=\"h-64 bg-slate-700 rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!settings) {\n    return (\n      <div className=\"text-center py-12\">\n        <AlertTriangle className=\"h-12 w-12 text-red-400 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-white mb-2\">Failed to Load Settings</h3>\n        <p className=\"text-slate-400\">Unable to load system settings. Please try again.</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-white\">System Settings</h1>\n          <p className=\"text-slate-400 mt-1\">Configure platform parameters and business rules</p>\n        </div>\n        <Button\n          onClick={handleSave}\n          loading={saving}\n          className=\"flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white\"\n        >\n          {saveSuccess ? (\n            <>\n              <CheckCircle className=\"h-4 w-4\" />\n              Saved\n            </>\n          ) : (\n            <>\n              <Save className=\"h-4 w-4\" />\n              Save Changes\n            </>\n          )}\n        </Button>\n      </div>\n\n      {/* Mining Unit Pricing */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-white\">\n            <Zap className=\"h-5 w-5 text-blue-400\" />\n            Mining Unit Pricing\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                THS Price (USD)\n              </label>\n              <div className=\"relative\">\n                <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.thsPriceUSD}\n                  onChange={(e) => updateSetting('thsPriceUSD', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500\"\n                />\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Minimum Purchase\n              </label>\n              <div className=\"relative\">\n                <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.minPurchaseAmount}\n                  onChange={(e) => updateSetting('minPurchaseAmount', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500\"\n                />\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Maximum Purchase\n              </label>\n              <div className=\"relative\">\n                <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.maxPurchaseAmount}\n                  onChange={(e) => updateSetting('maxPurchaseAmount', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500\"\n                />\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Earnings Configuration */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-white\">\n            <Percent className=\"h-5 w-5 text-orange-400\" />\n            Earnings Configuration\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Daily ROI (%)\n              </label>\n              <div className=\"relative\">\n                <Percent className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.dailyROIPercentage}\n                  onChange={(e) => updateSetting('dailyROIPercentage', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500\"\n                />\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Binary Bonus (%)\n              </label>\n              <div className=\"relative\">\n                <Percent className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.binaryBonusPercentage}\n                  onChange={(e) => updateSetting('binaryBonusPercentage', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500\"\n                />\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Referral Bonus (%)\n              </label>\n              <div className=\"relative\">\n                <Percent className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.referralBonusPercentage}\n                  onChange={(e) => updateSetting('referralBonusPercentage', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500\"\n                />\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Withdrawal Settings */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-white\">\n            <DollarSign className=\"h-5 w-5 text-red-400\" />\n            Withdrawal Settings\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Minimum Withdrawal\n              </label>\n              <div className=\"relative\">\n                <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.minWithdrawalAmount}\n                  onChange={(e) => updateSetting('minWithdrawalAmount', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500\"\n                />\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Withdrawal Fee (%)\n              </label>\n              <div className=\"relative\">\n                <Percent className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.withdrawalFeePercentage}\n                  onChange={(e) => updateSetting('withdrawalFeePercentage', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500\"\n                />\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Processing Days\n              </label>\n              <Input\n                type=\"number\"\n                value={settings.withdrawalProcessingDays}\n                onChange={(e) => updateSetting('withdrawalProcessingDays', parseInt(e.target.value) || 0)}\n                className=\"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500\"\n              />\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Platform Settings */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-white\">\n            <Settings className=\"h-5 w-5 text-blue-400\" />\n            Platform Settings\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Platform Fee (%)\n              </label>\n              <div className=\"relative\">\n                <Percent className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.platformFeePercentage}\n                  onChange={(e) => updateSetting('platformFeePercentage', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"space-y-4 pt-4 border-t border-slate-600\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h4 className=\"text-sm font-medium text-white\">Maintenance Mode</h4>\n                <p className=\"text-sm text-slate-400\">Temporarily disable platform access</p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.maintenanceMode}\n                  onChange={(e) => updateSetting('maintenanceMode', e.target.checked)}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n              </label>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h4 className=\"text-sm font-medium text-white\">Registration Enabled</h4>\n                <p className=\"text-sm text-slate-400\">Allow new user registrations</p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.registrationEnabled}\n                  onChange={(e) => updateSetting('registrationEnabled', e.target.checked)}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n              </label>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h4 className=\"text-sm font-medium text-white\">KYC Required</h4>\n                <p className=\"text-sm text-slate-400\">Require KYC verification for withdrawals</p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.kycRequired}\n                  onChange={(e) => updateSetting('kycRequired', e.target.checked)}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n              </label>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAwCO,MAAM,iBAA2B;;IACtC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,YAAY,KAAK,IAAI;gBACvB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,UAAU;YACV,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,eAAe;gBACf,WAAW,IAAM,eAAe,QAAQ;YAC1C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,gBAAgB,CAAC,KAA2B;QAChD,IAAI,CAAC,UAAU;QACf,YAAY;YAAE,GAAG,QAAQ;YAAE,CAAC,IAAI,EAAE;QAAM;IAC1C;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;8BACzB,6LAAC;oBAAG,WAAU;8BAAsC;;;;;;8BACpD,6LAAC;oBAAE,WAAU;8BAAiB;;;;;;;;;;;;IAGpC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAgC;;;;;;0CAC9C,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;kCAErC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,SAAS;wBACT,WAAU;kCAET,4BACC;;8CACE,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAY;;yDAIrC;;8CACE,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;0BAQpC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAA0B;;;;;;;;;;;;kCAI7C,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,cAAc,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDAC5E,WAAU;;;;;;;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,iBAAiB;oDACjC,UAAU,CAAC,IAAM,cAAc,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDAClF,WAAU;;;;;;;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,iBAAiB;oDACjC,UAAU,CAAC,IAAM,cAAc,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDAClF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA4B;;;;;;;;;;;;kCAInD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,kBAAkB;oDAClC,UAAU,CAAC,IAAM,cAAc,sBAAsB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDACnF,WAAU;;;;;;;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,qBAAqB;oDACrC,UAAU,CAAC,IAAM,cAAc,yBAAyB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDACtF,WAAU;;;;;;;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,uBAAuB;oDACvC,UAAU,CAAC,IAAM,cAAc,2BAA2B,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDACxF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAyB;;;;;;;;;;;;kCAInD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,mBAAmB;oDACnC,UAAU,CAAC,IAAM,cAAc,uBAAuB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDACpF,WAAU;;;;;;;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,uBAAuB;oDACvC,UAAU,CAAC,IAAM,cAAc,2BAA2B,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDACxF,WAAU;;;;;;;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,6LAAC,oIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO,SAAS,wBAAwB;4CACxC,UAAU,CAAC,IAAM,cAAc,4BAA4B,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4CACvF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAA0B;;;;;;;;;;;;kCAIlD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,qBAAqB;oDACrC,UAAU,CAAC,IAAM,cAAc,yBAAyB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDACtF,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAExC,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS,SAAS,eAAe;wDACjC,UAAU,CAAC,IAAM,cAAc,mBAAmB,EAAE,MAAM,CAAC,OAAO;wDAClE,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;kDAInB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAExC,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS,SAAS,mBAAmB;wDACrC,UAAU,CAAC,IAAM,cAAc,uBAAuB,EAAE,MAAM,CAAC,OAAO;wDACtE,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;kDAInB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAExC,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS,SAAS,WAAW;wDAC7B,UAAU,CAAC,IAAM,cAAc,eAAe,EAAE,MAAM,CAAC,OAAO;wDAC9D,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B;GAzWa;KAAA", "debugId": null}}, {"offset": {"line": 7358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/SystemLogs.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardHeader, CardTitle, CardContent, Button, Input } from '@/components/ui';\nimport { \n  FileText, \n  Search, \n  Filter, \n  Download,\n  Calendar,\n  User,\n  Activity,\n  AlertTriangle,\n  Info,\n  CheckCircle,\n  XCircle\n} from 'lucide-react';\nimport { formatDate } from '@/lib/utils';\n\ninterface SystemLog {\n  id: string;\n  action: string;\n  userId?: string;\n  user?: {\n    firstName: string;\n    lastName: string;\n    email: string;\n  };\n  details: any;\n  ipAddress: string;\n  userAgent: string;\n  createdAt: string;\n}\n\nexport const SystemLogs: React.FC = () => {\n  const [logs, setLogs] = useState<SystemLog[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterAction, setFilterAction] = useState<string>('all');\n  const [dateRange, setDateRange] = useState<'today' | 'week' | 'month' | 'all'>('today');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n\n  useEffect(() => {\n    fetchLogs();\n  }, [currentPage, searchTerm, filterAction, dateRange]);\n\n  const fetchLogs = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        page: currentPage.toString(),\n        limit: '50',\n        search: searchTerm,\n        action: filterAction,\n        dateRange,\n      });\n\n      const response = await fetch(`/api/admin/logs?${params}`, {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setLogs(data.data.logs);\n          setTotalPages(data.data.totalPages);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch logs:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const exportLogs = async () => {\n    try {\n      const params = new URLSearchParams({\n        search: searchTerm,\n        action: filterAction,\n        dateRange,\n        export: 'true',\n      });\n\n      const response = await fetch(`/api/admin/logs/export?${params}`, {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `system-logs-${new Date().toISOString().split('T')[0]}.csv`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n      }\n    } catch (error) {\n      console.error('Failed to export logs:', error);\n    }\n  };\n\n  const getActionIcon = (action: string) => {\n    switch (action) {\n      case 'USER_LOGIN':\n      case 'USER_LOGOUT':\n        return <User className=\"h-4 w-4 text-blue-400\" />;\n      case 'USER_REGISTER':\n        return <CheckCircle className=\"h-4 w-4 text-green-400\" />;\n      case 'MINING_PURCHASE':\n      case 'WITHDRAWAL_REQUEST':\n        return <Activity className=\"h-4 w-4 text-purple-400\" />;\n      case 'KYC_SUBMIT':\n      case 'KYC_APPROVE':\n      case 'KYC_REJECT':\n        return <FileText className=\"h-4 w-4 text-orange-400\" />;\n      case 'ADMIN_ACTION':\n        return <AlertTriangle className=\"h-4 w-4 text-red-400\" />;\n      default:\n        return <Info className=\"h-4 w-4 text-slate-400\" />;\n    }\n  };\n\n  const getActionColor = (action: string) => {\n    switch (action) {\n      case 'USER_LOGIN':\n      case 'USER_REGISTER':\n      case 'KYC_APPROVE':\n        return 'text-green-300 bg-green-900/20 border border-green-700';\n      case 'USER_LOGOUT':\n        return 'text-blue-300 bg-blue-900/20 border border-blue-700';\n      case 'MINING_PURCHASE':\n      case 'WITHDRAWAL_REQUEST':\n        return 'text-purple-300 bg-purple-900/20 border border-purple-700';\n      case 'KYC_SUBMIT':\n        return 'text-orange-300 bg-orange-900/20 border border-orange-700';\n      case 'KYC_REJECT':\n      case 'ADMIN_ACTION':\n        return 'text-red-300 bg-red-900/20 border border-red-700';\n      default:\n        return 'text-slate-300 bg-slate-700 border border-slate-600';\n    }\n  };\n\n  const formatLogDetails = (details: any) => {\n    if (typeof details === 'string') return details;\n    if (typeof details === 'object') {\n      return Object.entries(details)\n        .map(([key, value]) => `${key}: ${value}`)\n        .join(', ');\n    }\n    return JSON.stringify(details);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-slate-700 rounded w-1/4 mb-4\"></div>\n          <div className=\"h-64 bg-slate-700 rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-white\">System Logs</h1>\n          <p className=\"text-slate-400 mt-1\">Monitor platform activity and user actions</p>\n        </div>\n        <Button\n          onClick={exportLogs}\n          variant=\"outline\"\n          className=\"flex items-center gap-2 border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white\"\n        >\n          <Download className=\"h-4 w-4\" />\n          Export Logs\n        </Button>\n      </div>\n\n      {/* Filters */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div>\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  placeholder=\"Search logs...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500\"\n                />\n              </div>\n            </div>\n            <div>\n              <select\n                value={filterAction}\n                onChange={(e) => setFilterAction(e.target.value)}\n                className=\"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"all\">All Actions</option>\n                <option value=\"USER_LOGIN\">User Login</option>\n                <option value=\"USER_REGISTER\">User Register</option>\n                <option value=\"MINING_PURCHASE\">Mining Purchase</option>\n                <option value=\"WITHDRAWAL_REQUEST\">Withdrawal Request</option>\n                <option value=\"KYC_SUBMIT\">KYC Submit</option>\n                <option value=\"ADMIN_ACTION\">Admin Action</option>\n              </select>\n            </div>\n            <div>\n              <select\n                value={dateRange}\n                onChange={(e) => setDateRange(e.target.value as any)}\n                className=\"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"today\">Today</option>\n                <option value=\"week\">This Week</option>\n                <option value=\"month\">This Month</option>\n                <option value=\"all\">All Time</option>\n              </select>\n            </div>\n            <div className=\"text-sm text-slate-400 flex items-center\">\n              <Activity className=\"h-4 w-4 mr-1\" />\n              {logs.length} logs found\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Logs Table */}\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-white\">\n            <FileText className=\"h-5 w-5 text-blue-400\" />\n            Activity Logs\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-2\">\n            {logs.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <FileText className=\"h-12 w-12 text-slate-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-white mb-2\">No Logs Found</h3>\n                <p className=\"text-slate-400\">No activity logs match your current filters.</p>\n              </div>\n            ) : (\n              logs.map((log) => (\n                <div key={log.id} className=\"border border-slate-600 rounded-lg p-4 hover:bg-slate-700 transition-colors\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex items-start gap-3 flex-1\">\n                      <div className=\"mt-1\">\n                        {getActionIcon(log.action)}\n                      </div>\n\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-center gap-2 mb-1\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getActionColor(log.action)}`}>\n                            {log.action.replace(/_/g, ' ')}\n                          </span>\n                          <span className=\"text-sm text-slate-400\">\n                            {formatDate(log.createdAt)}\n                          </span>\n                        </div>\n\n                        {log.user && (\n                          <div className=\"text-sm text-slate-300 mb-1\">\n                            <span className=\"font-medium\">\n                              {log.user.firstName} {log.user.lastName}\n                            </span>\n                            <span className=\"text-slate-400 ml-2\">({log.user.email})</span>\n                          </div>\n                        )}\n\n                        <div className=\"text-sm text-slate-400 mb-2\">\n                          {formatLogDetails(log.details)}\n                        </div>\n\n                        <div className=\"flex items-center gap-4 text-xs text-slate-500\">\n                          <span>IP: {log.ipAddress}</span>\n                          <span className=\"truncate max-w-xs\">\n                            User Agent: {log.userAgent}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n\n          {/* Pagination */}\n          {totalPages > 1 && (\n            <div className=\"flex items-center justify-between mt-6 pt-6 border-t border-slate-600\">\n              <div className=\"text-sm text-slate-400\">\n                Page {currentPage} of {totalPages}\n              </div>\n              <div className=\"flex gap-2\">\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}\n                  disabled={currentPage === 1}\n                  className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white disabled:opacity-50\"\n                >\n                  Previous\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}\n                  disabled={currentPage === totalPages}\n                  className=\"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white disabled:opacity-50\"\n                >\n                  Next\n                </Button>\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;;;AAjBA;;;;;AAkCO,MAAM,aAAuB;;IAClC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsC;IAC/E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG;QAAC;QAAa;QAAY;QAAc;KAAU;IAErD,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,YAAY,QAAQ;gBAC1B,OAAO;gBACP,QAAQ;gBACR,QAAQ;gBACR;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,gBAAgB,EAAE,QAAQ,EAAE;gBACxD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,QAAQ,KAAK,IAAI,CAAC,IAAI;oBACtB,cAAc,KAAK,IAAI,CAAC,UAAU;gBACpC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,SAAS,IAAI,gBAAgB;gBACjC,QAAQ;gBACR,QAAQ;gBACR;gBACA,QAAQ;YACV;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,QAAQ,EAAE;gBAC/D,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,CAAC,YAAY,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;gBACxE,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,OAAO,YAAY,UAAU,OAAO;QACxC,IAAI,OAAO,YAAY,UAAU;YAC/B,OAAO,OAAO,OAAO,CAAC,SACnB,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,GAAG,IAAI,EAAE,EAAE,OAAO,EACxC,IAAI,CAAC;QACV;QACA,OAAO,KAAK,SAAS,CAAC;IACxB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAgC;;;;;;0CAC9C,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;kCAErC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,SAAQ;wBACR,WAAU;;0CAEV,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAMpC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CACC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;;;;;;0CAIhB,6LAAC;0CACC,cAAA,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAa;;;;;;sDAC3B,6LAAC;4CAAO,OAAM;sDAAgB;;;;;;sDAC9B,6LAAC;4CAAO,OAAM;sDAAkB;;;;;;sDAChC,6LAAC;4CAAO,OAAM;sDAAqB;;;;;;sDACnC,6LAAC;4CAAO,OAAM;sDAAa;;;;;;sDAC3B,6LAAC;4CAAO,OAAM;sDAAe;;;;;;;;;;;;;;;;;0CAGjC,6LAAC;0CACC,cAAA,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;sDACtB,6LAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;sDACtB,6LAAC;4CAAO,OAAM;sDAAM;;;;;;;;;;;;;;;;;0CAGxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACnB,KAAK,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;;;;;0BAOrB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAA0B;;;;;;;;;;;;kCAIlD,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC;gCAAI,WAAU;0CACZ,KAAK,MAAM,KAAK,kBACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,6LAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;2CAGhC,KAAK,GAAG,CAAC,CAAC,oBACR,6LAAC;wCAAiB,WAAU;kDAC1B,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,cAAc,IAAI,MAAM;;;;;;kEAG3B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAW,CAAC,wEAAwE,EAAE,eAAe,IAAI,MAAM,GAAG;kFACrH,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM;;;;;;kFAE5B,6LAAC;wEAAK,WAAU;kFACb,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,IAAI,SAAS;;;;;;;;;;;;4DAI5B,IAAI,IAAI,kBACP,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;;4EACb,IAAI,IAAI,CAAC,SAAS;4EAAC;4EAAE,IAAI,IAAI,CAAC,QAAQ;;;;;;;kFAEzC,6LAAC;wEAAK,WAAU;;4EAAsB;4EAAE,IAAI,IAAI,CAAC,KAAK;4EAAC;;;;;;;;;;;;;0EAI3D,6LAAC;gEAAI,WAAU;0EACZ,iBAAiB,IAAI,OAAO;;;;;;0EAG/B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;4EAAK;4EAAK,IAAI,SAAS;;;;;;;kFACxB,6LAAC;wEAAK,WAAU;;4EAAoB;4EACrB,IAAI,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAjC5B,IAAI,EAAE;;;;;;;;;;4BA6CrB,aAAa,mBACZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAAyB;4CAChC;4CAAY;4CAAK;;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;gDACzD,UAAU,gBAAgB;gDAC1B,WAAU;0DACX;;;;;;0DAGD,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,YAAY,OAAO;gDAClE,UAAU,gBAAgB;gDAC1B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAxSa;KAAA", "debugId": null}}, {"offset": {"line": 8108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/app/admin/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '@/hooks/useAuth';\nimport { useRouter } from 'next/navigation';\nimport { AdminLayout } from '@/components/admin/AdminLayout';\nimport { AdminDashboard } from '@/components/admin/AdminDashboard';\nimport { UserManagement } from '@/components/admin/UserManagement';\nimport { KYCReview } from '@/components/admin/KYCReview';\nimport { WithdrawalManagement } from '@/components/admin/WithdrawalManagement';\nimport { SystemSettings } from '@/components/admin/SystemSettings';\nimport { SystemLogs } from '@/components/admin/SystemLogs';\nimport { Loading } from '@/components/ui';\n\nexport default function AdminPage() {\n  const { user, loading } = useAuth();\n  const router = useRouter();\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [checkingAdmin, setCheckingAdmin] = useState(true);\n\n  // Check if user is admin\n  useEffect(() => {\n    const checkAdminStatus = async () => {\n      if (!loading && user) {\n        try {\n          const response = await fetch('/api/admin/check', {\n            credentials: 'include',\n          });\n          \n          if (response.ok) {\n            const data = await response.json();\n            setIsAdmin(data.isAdmin);\n          } else {\n            setIsAdmin(false);\n          }\n        } catch (error) {\n          console.error('Error checking admin status:', error);\n          setIsAdmin(false);\n        }\n      }\n      setCheckingAdmin(false);\n    };\n\n    checkAdminStatus();\n  }, [user, loading]);\n\n  // Redirect if not authenticated\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login');\n    }\n  }, [user, loading, router]);\n\n  // Redirect if not admin\n  useEffect(() => {\n    if (!checkingAdmin && !isAdmin && user) {\n      router.push('/dashboard');\n    }\n  }, [isAdmin, checkingAdmin, user, router]);\n\n  if (loading || checkingAdmin) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Loading size=\"lg\" text=\"Loading admin panel...\" />\n      </div>\n    );\n  }\n\n  if (!user || !isAdmin) {\n    return null;\n  }\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'dashboard':\n        return <AdminDashboard />;\n      case 'users':\n        return <UserManagement />;\n      case 'kyc':\n        return <KYCReview />;\n      case 'withdrawals':\n        return <WithdrawalManagement />;\n      case 'settings':\n        return <SystemSettings />;\n      case 'logs':\n        return <SystemLogs />;\n      default:\n        return <AdminDashboard />;\n    }\n  };\n\n  return (\n    <AdminLayout activeTab={activeTab} onTabChange={setActiveTab}>\n      {renderTabContent()}\n    </AdminLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AAZA;;;;;;;;;;;;AAce,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;wDAAmB;oBACvB,IAAI,CAAC,WAAW,MAAM;wBACpB,IAAI;4BACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gCAC/C,aAAa;4BACf;4BAEA,IAAI,SAAS,EAAE,EAAE;gCACf,MAAM,OAAO,MAAM,SAAS,IAAI;gCAChC,WAAW,KAAK,OAAO;4BACzB,OAAO;gCACL,WAAW;4BACb;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,gCAAgC;4BAC9C,WAAW;wBACb;oBACF;oBACA,iBAAiB;gBACnB;;YAEA;QACF;8BAAG;QAAC;QAAM;KAAQ;IAElB,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;8BAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,iBAAiB,CAAC,WAAW,MAAM;gBACtC,OAAO,IAAI,CAAC;YACd;QACF;8BAAG;QAAC;QAAS;QAAe;QAAM;KAAO;IAEzC,IAAI,WAAW,eAAe;QAC5B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,sIAAA,CAAA,UAAO;gBAAC,MAAK;gBAAK,MAAK;;;;;;;;;;;IAG9B;IAEA,IAAI,CAAC,QAAQ,CAAC,SAAS;QACrB,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,gJAAA,CAAA,iBAAc;;;;;YACxB,KAAK;gBACH,qBAAO,6LAAC,gJAAA,CAAA,iBAAc;;;;;YACxB,KAAK;gBACH,qBAAO,6LAAC,2IAAA,CAAA,YAAS;;;;;YACnB,KAAK;gBACH,qBAAO,6LAAC,sJAAA,CAAA,uBAAoB;;;;;YAC9B,KAAK;gBACH,qBAAO,6LAAC,gJAAA,CAAA,iBAAc;;;;;YACxB,KAAK;gBACH,qBAAO,6LAAC,4IAAA,CAAA,aAAU;;;;;YACpB;gBACE,qBAAO,6LAAC,gJAAA,CAAA,iBAAc;;;;;QAC1B;IACF;IAEA,qBACE,6LAAC,6IAAA,CAAA,cAAW;QAAC,WAAW;QAAW,aAAa;kBAC7C;;;;;;AAGP;GAnFwB;;QACI,2HAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}